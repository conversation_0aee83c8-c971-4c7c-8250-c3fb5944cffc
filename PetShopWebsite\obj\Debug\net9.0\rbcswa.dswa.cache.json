{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["k7VIu0hN2i9gusn0qKzHk2mHBw1Qnkn97EJQqVyek7g=", "badH9BGBTadSaYg5rfDW7oNxCdPPRWjB50z5U1/EAxU=", "buP8BI4Q4bLywhTBQtSF0v+hJPWvZn8KkNhg74BkE70=", "EQ9m3t7koPR8GDSdLnLLvbIJi01YHKJC018ksgiLxVo=", "4zedJQ6WPRR7AZybnui/V8U/fjjeI6UtnZLfIg/JmkE=", "vv3aG/xO+KBeH16UWbgZGAHwtBUH6MAgLTJFbcqBPRU=", "NCDiP0/3qjh6izeQeSTMct3VcC2UgyPB8sNp9Eez/D0=", "kastxcLXx2A+RoZ7EeUA55kPlKNP3E1millb8pPdqAQ=", "zfdcHaOOFM2ZawWZq/ZOlLljGs6/YvMfhz4mQstoRN8=", "mjq7G64a98SOYajiF6Bt7nQQCCBKGe4C2EL0/ZRgp1c=", "GKF9ZZwOo4SDgsRs0gJYPObuiDOM8teMcWnZux2LhX4=", "ZY1SL3QJKxnwwhOAcO87/gW4cOuL0STmA/joX6/tEEw=", "+Dfv/kHLyp/V/l8RBrh3hozkPc8qsv+WKRFNK3jfdLE=", "8OypJS4rgLtAY0lk3HtlUjsiLllV2LntNX/++iJFDKA=", "7e7qZVItAXlNv2i+sYbh41olBzN2Y/EWfkUqrPmAWUk=", "VKyBq8MWbTSBvYOf8Z0A5fLIqBits4Wx8EvJSkEA8wQ=", "xK3c6niU+Fw1D/FsfdY6hS5Vh6tCUyPkwD1bUevdSUM=", "KUD5/HwjNmjGtr2Cmd/7QFmIs4P31929wIX7S6tHszM=", "mMzWU7pgjWgw3iuKYCJVAqKS2RnmQJz9b5vn5vWzYZs=", "+l8lxqUnWNUKiGjAIlMxMnE9RQZWOdJPB3M8URbyz0k=", "uu0odewCjA2T2DBbAzQEX2sTknICJWzcISicz690aS4=", "M4KjRHbcWTVpyrSTGDtTAsb48/SDgwiWs32yCgcVpZg=", "lBnEBjuSTGaqG6NCok5wDUzgkAlfu0KFOQP6NuCR+hg=", "xxzpkhlDXdhOSFBloZF7Da14qsFjMQctQHmrjwiXHuQ=", "o78LtQPMDv3WtYd1N6cihxEH/1gLezlC50B8C7JSJXI=", "JVcvFA2duy7bFBUbcUV/xX1VlOr4NbXJnqnbOR5GWaE=", "9yDI27VFE1FzXXn4j+UKjcW496CFFATApg5cl/+dXhc=", "vJTXXZ5zV7ZFSpWozLvR+nG92xZWx1pPZ3inD4gQJvk=", "K2dzedsGtzuPyGCEnEgHqpZUCN6m8VXwlUvS/kU6tDw=", "8mhCKZMlWPCdsmNykf6f8atr3FZSeaa5zw9il4jVd/8=", "V8+kDUfNzJMq563rRGu1BwcryP+VPkhr4u/OWQF8fN8=", "TzAHrGIGbXAxN4NCkwOYJI+/VxTx2/15M59hGgdj/cg=", "QKl3TxCS/aZXfSFGjD8SYueM0V1+QxBSpLj6LnIq2Pk=", "FmodMwPAxHkSa886djHLChaWV/B3OhemdQ5Icq5m48s=", "iyNhKTxme0dwJOZmzgTxzJj8p4Cl6c86nrXiluNOZn0=", "odQok6nRrYskyXmn+vfqLwY5r702ryWOjn4LlOnzaEA=", "8LXBwrUpKezm69xoqEHLpI6mJ3VSTVQr4hJQ/Y4vpuw=", "UyRYTFBtKsSMA4MGmRZNA3Anszpr+hOEN6q526hB3U8=", "6vdbd7jKda+qRPnd3AG92jeTfn3AgL0IOlmkgm8CmUA=", "ueKWMd1/h41ZXjIDFQelCBBEYASFcY4bnqJqG7dWGKI=", "EwwtQvoiUfLoLVMV2mWNw9oJWgcqDJPp6eXeB+o6KaY=", "LB/RM1lbIq5jdzD/ufzp43orTR3A3VZawMd/vrgOD80=", "VId2UuAJGHscaTjReh22Xa9EIel7KhHGayMQ/HuTICg=", "aY8X8ncwO4q9gS/9/vU38yVPCuxzRR+UHErUzGY7HIQ=", "nKs9NnCfp525t1Edg8Xa6YDXRHeUbf9Ytq/Oz3wxYkQ=", "9Vj+xd7ypH+vks8tYMsNgcanJJ1b+TLGtPF2GspwEKY=", "C9MO1dxN2Jex/crzRSOl1jMlqSsOXy6UAAFrdTm1abs=", "+hGsMvrxPMlzVnTAI+je5/1ni0QKktNT+QKsjdDeUXY=", "/pt4kXWdBlxasZh77JdaB7dWpRWkgerK3blA0v43vso=", "UH6XoY+vmZ5rcJDhz4Tz1CqUH8Bsr8RV/ItvBEWQoo0=", "mPGVILg9R4bqU3QAbEXh6tNYJiUZBBmHiXxsSjpgOWA=", "+zw7XS2lzxNjsFH7x2KNC/UNTAbVRv4eUph1wb3IvkQ=", "Tfgb3kMtGnAByxGULt6ANkCJxQlIfSup18F/ZvTmFCo=", "RIJgTWMzN9PKrpgkgZ8eq6pongCw2/IPwBg49EBh6iA=", "bWr1vysYtJ/N5xIzJa6TzSYqae15JhH5VoGZdGiJSeE=", "AqP2uK3O7w6V1ZWpsuHCCqbOYLTusVL/zXg2RTp9yXc=", "Q5gVXkRO6J3jCilIm0ApPZnkYLdm0V7QElY9KxNPWfQ=", "jWb/Hz7XRt1GkRPjvWh2iBPig9oslXVRd5o174N+Xi4=", "4F+dyAEgrh3X9e677ML2BLOCq3l0h73Q2766uzgTHg8=", "FwRedYP3HwduSG8DuHnGYNzQip2Cgy0x3qpZlxhoxbM=", "UMxPaYxXZHAmyke93wEhTqWIc4MgiYDacRp/umYG+HU=", "C0mTc2iv8h1Lk56qfO7LsHlAFi5HhjdxlpaxZzAzzbI=", "YuZokSuttwF5bXAgWchoaz/QZ/yyjNHNkugm5QJkmyU=", "L5NQhUkfJXZUlK+wXBsi28A/+R/6Dk0BYvQgbt9jbN4=", "p56hif/KvN+teQjEJHkyJMd/Vxo7HNBz1A8jGsdKlFU=", "MSynSXWs81ZXO7XxyNQMmDqHWNIoHBITxn8xWsCIbTw=", "GOrqc1MfdeJafaTHtZZosrHX6nUG5LlEJXHQrQqQ1Ps=", "unyvYyibzSvjq8mXSyUFkkebN0Tt68xRiKIVL4594rg=", "nqy9V91lBb8cPD8lAAQIi660vzLVBcwiKcEL7+8KokM=", "3EAdl6AdmTy8y/1ovq2Lzv1uL5uBfqNhLILWv0AXBwc=", "NDGtj4tTzm64ymmuDQBpdZ0cuSSeUHfx9nBpUuriSG8=", "ql/PtTrHfcDnK8NWIEMu4e4Ar/6OI6yGSXYAojCQ8Mc=", "SqPE4GxhMdNzlikt85RMISX4wO6NFU7BYP7a+NbWPwU=", "JBnYF3TnrH3oEYAWH16tgQSyE+ABXS2j4LgexldeuGQ=", "A+arhMa5KuirJgCLAowkTVGO/SY75IxmhYBPgh9KZUs=", "U8OnTZWjmr+ZMu0NMqXyL7iQDCsdvY2KgbS6aa6d/cA=", "FTZ3fLTghEsZDYLaewsWtFvNvoKY7SxydBHpGK8Rkjc=", "EHVxYY4R7I4OvvEoVEK8xeVIXd9NCPkVP2WIrseEvL4=", "SN1iCsHpMvcPjn+18yrAAoYF33bAnCjRigpvQEaA35c=", "HK4qQ/lB2x3ItAlsHcp2pSZbp7jPOUSGxJOJJgzNKok=", "MglLg97tA76te/atOUoHz+skICtutV6zLM5CNegbz5U=", "nHv1QJ5IZPiBKEZjsCv/bkaqZlvjtwD/pRGmoxdlzxc=", "jFKCt3pBwRd5pGG+VmVvUeilr9zcu+bbmBYfLwhKNUM=", "vrYVc8ogUnSUGlS0QNE8+os/h0upbd1jOv3VAYQ4pmI=", "V7S4P+SFrGrxFfj/k3pajWF486wgmzlDEufEFrfud4Q=", "LwHFhlPgN0ssFdbq5bQyRPxzQQTsE8ssuTR+b2aObJI=", "9Y20Lh5nh6xb/LNt7HGPUGODOqr++i/6R9TlpSy5MFQ=", "OrTz1/N7wvtFbHqKTtlIHad4bUNJQkIoc82doV5N/aI=", "lfG/GUGJ0uTesxqVh/Jyv3qsjqXCCcZREX5Rwx2byRA=", "41p5it8sD+VBxpS5uVa+OGCJuoOUMj+Ji9jKGRrBinA=", "s4dHvPaf9t8RBcKxqYg393fGIf4eSERX8ms+txnrKCY=", "M2Z4+9UnMQ9vCxp1S7ck143ZlSBTpHmeM2agM+0Vpig=", "+9xzEFrhXYFdcCWucT8skNlPwlGuOVKtm6aIfDQPxQE=", "q8ydXmJ22DYrA6uMQCumWPsxT4QTEUFakOZmtDmZLqE=", "9fuRBrHxlz5yV5HKb7OUXpDZMKMp4za9Yop/buwo2mI=", "UXtWR/VJ0QvwJsEEZG9X5ntD8BjaN6B1lCWg8RwnlJk=", "EKPa964QKQEraayLLFIAi/fpZ98GKbLXnpeUrDHu6IE=", "OsIhQWqTniRRthOtYV8pw4rKvLt77lCAcFvdsFkLlpE=", "ipTuqQl7eQ2HNbB34VpHVRkfjqeDH0xy/LwynBRol/o=", "WhLLw1nJ9yvXpNd8FE0CH3GeO1GVwBt92NjGe3dqCkw=", "pTRIsomjWFKrrqIwvMUOqsI0Up5YU5semYcPOfwc0/w=", "QiDDOI8VTxpMr5fyC+6JRLebtjnvOPd6fcp8NoIyvZY=", "qlhWSR2l8yRzG5AbSdAikjZBgSg1eq3alHa/DycfvCU=", "Lj4kqcFke+NOnln/WYR0EL5+JJsZRGnvfu6M5B+Rsvs=", "aPFDsVs7yoPGPr0GjyGGAKo/rwdX8w7ky1pAf5k6290=", "69NMqvEBFkdkzIoLBUWObp3B9Cppud5Sl+q4qdmYxeA=", "ENAde4ad590yx1rGM+ORKhNgaW6+cMOh7FW8Y46ODls=", "dBa8iCwKi3/abXXwM1+F5RCivdFkA5iSB1RyVJGs2f8=", "+n75QEuOt1d2y8s78AO4+ZDzmMjtGVkj0LitB3hylRg=", "MyL15Fzh+TFTGurulhHa1xvU+l3iEIsoWqmOuKcTUG8=", "rVjsDG3D2Witz3BLg1xsOfvpJmJ4xwKCFMizyhn/k5U=", "GQYANLIM2GnkLMCEwumdaNB0zdY7WinFCkbquI0C1wQ=", "G6sRZB6hiwKjblphMndd6DOIRmqIurHtN2R9f3ydUdk=", "XJTUdZxqOXXB8+J/F8zPoWAEAnI94NMk89nELxiksus=", "x4A8UFfLM7phlrOwtApXuLp5LuSE8EsTl2ycxl3gPfs=", "JD+zJvuYHiEXKVojzKQkUwXfl/TYKviF266+aTD0/7w=", "520qSHyJkK6ijCU9Z0TDtB+HqRx74w+Ceu39Xw+vJKI=", "bFFD15hj+3P0c2v7ePmJTdoOvr6leberxyh1N48dJ+8=", "tmGBrbHSZ566oO4q24S09D3gMPv1EQJ4iV0QhP/ISSI=", "03VhJHESqNU36MyAxDiaecctSqPW81PWkyVnpk8+u/U=", "h72nnd/PFpr/HLNf8eXx7ORYfQSb2916GK0r6KYAhIU=", "cwp3SWxSWa0WKpKlakZI+jwi+W9t8TOFO8EgbryS+ZU=", "lVveZ3MCmXB3pjUEKNiM8F9/1zyb+6jAgPMTA8hbKFg=", "LU1dQEyV1gENf6Yh4Y0mxGZ1xcvRstF64J5Os0a/ktg=", "bYKJAcZxIE3vCcg/TfzS9pFF44PhiVli9q10hpDyWuM=", "hokl255loNqZBPYPus/rO2szxZnzk/yvAqn1cTR4Ho8="], "CachedAssets": {"k7VIu0hN2i9gusn0qKzHk2mHBw1Qnkn97EJQqVyek7g=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-07-20T15:28:51.7214414+00:00"}, "badH9BGBTadSaYg5rfDW7oNxCdPPRWjB50z5U1/EAxU=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-07-20T15:28:51.7234379+00:00"}, "buP8BI4Q4bLywhTBQtSF0v+hJPWvZn8KkNhg74BkE70=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-20T15:28:51.7244378+00:00"}, "EQ9m3t7koPR8GDSdLnLLvbIJi01YHKJC018ksgiLxVo=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-20T15:28:51.7464603+00:00"}, "4zedJQ6WPRR7AZybnui/V8U/fjjeI6UtnZLfIg/JmkE=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-20T15:28:51.7558944+00:00"}, "vv3aG/xO+KBeH16UWbgZGAHwtBUH6MAgLTJFbcqBPRU=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-20T15:28:51.7588959+00:00"}, "NCDiP0/3qjh6izeQeSTMct3VcC2UgyPB8sNp9Eez/D0=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-20T15:28:51.7666015+00:00"}, "kastxcLXx2A+RoZ7EeUA55kPlKNP3E1millb8pPdqAQ=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-20T15:28:51.7746086+00:00"}, "zfdcHaOOFM2ZawWZq/ZOlLljGs6/YvMfhz4mQstoRN8=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-20T15:28:51.7777102+00:00"}, "mjq7G64a98SOYajiF6Bt7nQQCCBKGe4C2EL0/ZRgp1c=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-20T15:28:51.78051+00:00"}, "GKF9ZZwOo4SDgsRs0gJYPObuiDOM8teMcWnZux2LhX4=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-20T15:28:51.7234379+00:00"}, "ZY1SL3QJKxnwwhOAcO87/gW4cOuL0STmA/joX6/tEEw=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-20T15:28:51.7260595+00:00"}, "+Dfv/kHLyp/V/l8RBrh3hozkPc8qsv+WKRFNK3jfdLE=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-20T15:28:51.7528916+00:00"}, "8OypJS4rgLtAY0lk3HtlUjsiLllV2LntNX/++iJFDKA=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-20T15:28:51.7558944+00:00"}, "7e7qZVItAXlNv2i+sYbh41olBzN2Y/EWfkUqrPmAWUk=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-20T15:28:51.7588959+00:00"}, "VKyBq8MWbTSBvYOf8Z0A5fLIqBits4Wx8EvJSkEA8wQ=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-20T15:28:51.7628971+00:00"}, "xK3c6niU+Fw1D/FsfdY6hS5Vh6tCUyPkwD1bUevdSUM=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-20T15:28:51.7726079+00:00"}, "KUD5/HwjNmjGtr2Cmd/7QFmIs4P31929wIX7S6tHszM=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-20T15:28:51.7716085+00:00"}, "mMzWU7pgjWgw3iuKYCJVAqKS2RnmQJz9b5vn5vWzYZs=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-20T15:28:51.7835186+00:00"}, "+l8lxqUnWNUKiGjAIlMxMnE9RQZWOdJPB3M8URbyz0k=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-20T15:28:51.8177353+00:00"}, "uu0odewCjA2T2DBbAzQEX2sTknICJWzcISicz690aS4=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-20T15:28:51.7260595+00:00"}, "M4KjRHbcWTVpyrSTGDtTAsb48/SDgwiWs32yCgcVpZg=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-20T15:28:51.7548972+00:00"}, "lBnEBjuSTGaqG6NCok5wDUzgkAlfu0KFOQP6NuCR+hg=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-20T15:28:51.7628971+00:00"}, "xxzpkhlDXdhOSFBloZF7Da14qsFjMQctQHmrjwiXHuQ=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-20T15:28:51.7726079+00:00"}, "o78LtQPMDv3WtYd1N6cihxEH/1gLezlC50B8C7JSJXI=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-20T15:28:51.7798049+00:00"}, "JVcvFA2duy7bFBUbcUV/xX1VlOr4NbXJnqnbOR5GWaE=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-20T15:28:51.7815182+00:00"}, "9yDI27VFE1FzXXn4j+UKjcW496CFFATApg5cl/+dXhc=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-20T15:28:51.8067375+00:00"}, "vJTXXZ5zV7ZFSpWozLvR+nG92xZWx1pPZ3inD4gQJvk=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-20T15:28:51.7767125+00:00"}, "K2dzedsGtzuPyGCEnEgHqpZUCN6m8VXwlUvS/kU6tDw=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-20T15:28:51.787516+00:00"}, "8mhCKZMlWPCdsmNykf6f8atr3FZSeaa5zw9il4jVd/8=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-20T15:28:51.8117352+00:00"}, "V8+kDUfNzJMq563rRGu1BwcryP+VPkhr4u/OWQF8fN8=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-20T15:28:51.7346133+00:00"}, "TzAHrGIGbXAxN4NCkwOYJI+/VxTx2/15M59hGgdj/cg=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-20T15:28:51.7726079+00:00"}, "QKl3TxCS/aZXfSFGjD8SYueM0V1+QxBSpLj6LnIq2Pk=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-20T15:28:51.7987348+00:00"}, "FmodMwPAxHkSa886djHLChaWV/B3OhemdQ5Icq5m48s=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-20T15:28:51.807736+00:00"}, "iyNhKTxme0dwJOZmzgTxzJj8p4Cl6c86nrXiluNOZn0=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-20T15:28:51.8217355+00:00"}, "odQok6nRrYskyXmn+vfqLwY5r702ryWOjn4LlOnzaEA=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-20T15:28:51.8277388+00:00"}, "8LXBwrUpKezm69xoqEHLpI6mJ3VSTVQr4hJQ/Y4vpuw=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-20T15:28:51.8451137+00:00"}, "UyRYTFBtKsSMA4MGmRZNA3Anszpr+hOEN6q526hB3U8=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-20T15:28:51.8591185+00:00"}, "6vdbd7jKda+qRPnd3AG92jeTfn3AgL0IOlmkgm8CmUA=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-20T15:28:51.8708789+00:00"}, "ueKWMd1/h41ZXjIDFQelCBBEYASFcY4bnqJqG7dWGKI=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-20T15:28:51.8769523+00:00"}, "EwwtQvoiUfLoLVMV2mWNw9oJWgcqDJPp6eXeB+o6KaY=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-20T15:28:51.7311103+00:00"}, "LB/RM1lbIq5jdzD/ufzp43orTR3A3VZawMd/vrgOD80=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-20T15:28:51.7474693+00:00"}, "VId2UuAJGHscaTjReh22Xa9EIel7KhHGayMQ/HuTICg=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-20T15:28:51.7568945+00:00"}, "aY8X8ncwO4q9gS/9/vU38yVPCuxzRR+UHErUzGY7HIQ=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-20T15:28:51.7686133+00:00"}, "nKs9NnCfp525t1Edg8Xa6YDXRHeUbf9Ytq/Oz3wxYkQ=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-20T15:28:51.7815182+00:00"}, "9Vj+xd7ypH+vks8tYMsNgcanJJ1b+TLGtPF2GspwEKY=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-20T15:28:51.8017364+00:00"}, "C9MO1dxN2Jex/crzRSOl1jMlqSsOXy6UAAFrdTm1abs=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-20T15:28:51.807736+00:00"}, "+hGsMvrxPMlzVnTAI+je5/1ni0QKktNT+QKsjdDeUXY=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-20T15:28:51.8037357+00:00"}, "/pt4kXWdBlxasZh77JdaB7dWpRWkgerK3blA0v43vso=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-20T15:28:51.807736+00:00"}, "UH6XoY+vmZ5rcJDhz4Tz1CqUH8Bsr8RV/ItvBEWQoo0=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-20T15:28:51.8117352+00:00"}, "mPGVILg9R4bqU3QAbEXh6tNYJiUZBBmHiXxsSjpgOWA=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-20T15:28:51.7224381+00:00"}, "+zw7XS2lzxNjsFH7x2KNC/UNTAbVRv4eUph1wb3IvkQ=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-20T15:28:51.7464603+00:00"}, "Tfgb3kMtGnAByxGULt6ANkCJxQlIfSup18F/ZvTmFCo=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-20T15:28:51.7494689+00:00"}, "RIJgTWMzN9PKrpgkgZ8eq6pongCw2/IPwBg49EBh6iA=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-20T15:28:51.7588959+00:00"}, "bWr1vysYtJ/N5xIzJa6TzSYqae15JhH5VoGZdGiJSeE=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-20T15:28:51.7628971+00:00"}, "AqP2uK3O7w6V1ZWpsuHCCqbOYLTusVL/zXg2RTp9yXc=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-20T15:28:51.7716085+00:00"}, "Q5gVXkRO6J3jCilIm0ApPZnkYLdm0V7QElY9KxNPWfQ=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-20T15:28:51.7757036+00:00"}, "jWb/Hz7XRt1GkRPjvWh2iBPig9oslXVRd5o174N+Xi4=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-20T15:28:51.7825177+00:00"}, "4F+dyAEgrh3X9e677ML2BLOCq3l0h73Q2766uzgTHg8=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-20T15:28:51.8007345+00:00"}, "FwRedYP3HwduSG8DuHnGYNzQip2Cgy0x3qpZlxhoxbM=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-20T15:28:51.8037357+00:00"}, "UMxPaYxXZHAmyke93wEhTqWIc4MgiYDacRp/umYG+HU=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-20T15:28:51.7311103+00:00"}, "C0mTc2iv8h1Lk56qfO7LsHlAFi5HhjdxlpaxZzAzzbI=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-20T15:28:51.7825177+00:00"}, "YuZokSuttwF5bXAgWchoaz/QZ/yyjNHNkugm5QJkmyU=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\unkdcwxcpt-5aab1of5qx.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "css/site#[.{fingerprint=5aab1of5qx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fapy7e31is", "Integrity": "eXyxwIroRcmLyXjDynOlYH34Vd/QvpZ+5DzyAbcUrhQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\css\\site.css", "FileLength": 1586, "LastWriteTime": "2025-07-20T16:10:49.7011063+00:00"}, "L5NQhUkfJXZUlK+wXBsi28A/+R/6Dk0BYvQgbt9jbN4=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\wcykn2hob7-61n19gt1b8.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-07-20T15:28:51.7957348+00:00"}, "p56hif/KvN+teQjEJHkyJMd/Vxo7HNBz1A8jGsdKlFU=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\27tgd60n0m-xtxxf3hu2r.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-20T15:28:51.7957348+00:00"}, "MSynSXWs81ZXO7XxyNQMmDqHWNIoHBITxn8xWsCIbTw=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\qwxw6kgjt4-bqjiyaj88i.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-20T15:28:51.7967338+00:00"}, "GOrqc1MfdeJafaTHtZZosrHX6nUG5LlEJXHQrQqQ1Ps=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\t7mi41czv9-c2jlpeoesf.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-20T15:28:51.8007345+00:00"}, "unyvYyibzSvjq8mXSyUFkkebN0Tt68xRiKIVL4594rg=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\1mh8zthlmz-erw9l3u2r3.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-20T15:28:51.8017364+00:00"}, "nqy9V91lBb8cPD8lAAQIi660vzLVBcwiKcEL7+8KokM=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\hdd822oiw3-aexeepp0ev.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-20T15:28:51.8037357+00:00"}, "3EAdl6AdmTy8y/1ovq2Lzv1uL5uBfqNhLILWv0AXBwc=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\35po9mda4t-d7shbmvgxk.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-20T15:28:51.8057363+00:00"}, "NDGtj4tTzm64ymmuDQBpdZ0cuSSeUHfx9nBpUuriSG8=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\v8jbnghxwu-ausgxo2sd3.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-20T15:28:51.7234379+00:00"}, "ql/PtTrHfcDnK8NWIEMu4e4Ar/6OI6yGSXYAojCQ8Mc=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\vxee2bhubr-k8d9w2qqmf.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-20T15:28:51.727112+00:00"}, "SqPE4GxhMdNzlikt85RMISX4wO6NFU7BYP7a+NbWPwU=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\bgixm7at1z-cosvhxvwiu.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-20T15:28:51.7825177+00:00"}, "JBnYF3TnrH3oEYAWH16tgQSyE+ABXS2j4LgexldeuGQ=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\dimrkjnbdz-ub07r2b239.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-20T15:28:51.8067375+00:00"}, "A+arhMa5KuirJgCLAowkTVGO/SY75IxmhYBPgh9KZUs=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\tak4eyysz1-fvhpjtyr6v.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-20T15:28:51.8097353+00:00"}, "U8OnTZWjmr+ZMu0NMqXyL7iQDCsdvY2KgbS6aa6d/cA=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\5unjx7zi5k-b7pk76d08c.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-20T15:28:51.8117352+00:00"}, "FTZ3fLTghEsZDYLaewsWtFvNvoKY7SxydBHpGK8Rkjc=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\eqb6uv2f48-fsbi9cje9m.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-20T15:28:51.812735+00:00"}, "EHVxYY4R7I4OvvEoVEK8xeVIXd9NCPkVP2WIrseEvL4=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\2ml8j51qkm-rzd6atqjts.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-20T15:28:51.8047409+00:00"}, "SN1iCsHpMvcPjn+18yrAAoYF33bAnCjRigpvQEaA35c=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\tsfq057yxq-ee0r1s7dh0.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-20T15:28:51.8087333+00:00"}, "HK4qQ/lB2x3ItAlsHcp2pSZbp7jPOUSGxJOJJgzNKok=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\i5id8qkixg-dxx9fxp4il.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-20T15:28:51.8097353+00:00"}, "MglLg97tA76te/atOUoHz+skICtutV6zLM5CNegbz5U=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\g8unagjsi1-jd9uben2k1.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-20T15:28:51.7214414+00:00"}, "nHv1QJ5IZPiBKEZjsCv/bkaqZlvjtwD/pRGmoxdlzxc=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\auhez7zrn9-khv3u5hwcm.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-20T15:28:51.7244378+00:00"}, "jFKCt3pBwRd5pGG+VmVvUeilr9zcu+bbmBYfLwhKNUM=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\rz8s4dqh4s-r4e9w2rdcm.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-20T15:28:51.7548972+00:00"}, "vrYVc8ogUnSUGlS0QNE8+os/h0upbd1jOv3VAYQ4pmI=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\5kcead20ha-lcd1t2u6c8.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-20T15:28:51.7568945+00:00"}, "V7S4P+SFrGrxFfj/k3pajWF486wgmzlDEufEFrfud4Q=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\s9edwhgncq-c2oey78nd0.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-20T15:28:51.7706079+00:00"}, "LwHFhlPgN0ssFdbq5bQyRPxzQQTsE8ssuTR+b2aObJI=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\giv6uzhzfg-tdbxkamptv.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-20T15:28:51.7757036+00:00"}, "9Y20Lh5nh6xb/LNt7HGPUGODOqr++i/6R9TlpSy5MFQ=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\szpko8dvkl-j5mq2jizvt.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-20T15:28:51.7815182+00:00"}, "OrTz1/N7wvtFbHqKTtlIHad4bUNJQkIoc82doV5N/aI=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\tr9cy6nyno-06098lyss8.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-20T15:28:51.8137337+00:00"}, "lfG/GUGJ0uTesxqVh/Jyv3qsjqXCCcZREX5Rwx2byRA=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\acjewkgan1-nvvlpmu67g.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-20T15:28:51.8167348+00:00"}, "41p5it8sD+VBxpS5uVa+OGCJuoOUMj+Ji9jKGRrBinA=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\f4s2yae38v-s35ty4nyc5.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-20T15:28:51.8207356+00:00"}, "s4dHvPaf9t8RBcKxqYg393fGIf4eSERX8ms+txnrKCY=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\otu2r47igv-pj5nd1wqec.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-20T15:28:51.7746086+00:00"}, "M2Z4+9UnMQ9vCxp1S7ck143ZlSBTpHmeM2agM+0Vpig=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\m8eh0ve9eo-46ein0sx1k.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-20T15:28:51.7917267+00:00"}, "+9xzEFrhXYFdcCWucT8skNlPwlGuOVKtm6aIfDQPxQE=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\aij9rob8iw-v0zj4ognzu.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-20T15:28:51.8037357+00:00"}, "q8ydXmJ22DYrA6uMQCumWPsxT4QTEUFakOZmtDmZLqE=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\uxekyftgev-37tfw0ft22.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-20T15:28:51.807736+00:00"}, "9fuRBrHxlz5yV5HKb7OUXpDZMKMp4za9Yop/buwo2mI=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\fxk4op7pfo-hrwsygsryq.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-20T15:28:51.8177353+00:00"}, "UXtWR/VJ0QvwJsEEZG9X5ntD8BjaN6B1lCWg8RwnlJk=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\j41d3pcjd7-pk9g2wxc8p.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-20T15:28:51.8217355+00:00"}, "EKPa964QKQEraayLLFIAi/fpZ98GKbLXnpeUrDHu6IE=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\3soxljptz4-ft3s53vfgj.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-20T15:28:51.8350849+00:00"}, "OsIhQWqTniRRthOtYV8pw4rKvLt77lCAcFvdsFkLlpE=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\m8lk5urw5q-6cfz1n2cew.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-20T15:28:51.810734+00:00"}, "ipTuqQl7eQ2HNbB34VpHVRkfjqeDH0xy/LwynBRol/o=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\eqt0fmxebo-6pdc2jztkx.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-20T15:28:51.8207356+00:00"}, "WhLLw1nJ9yvXpNd8FE0CH3GeO1GVwBt92NjGe3dqCkw=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\wvz7z6dr5f-493y06b0oq.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-20T15:28:51.8227391+00:00"}, "pTRIsomjWFKrrqIwvMUOqsI0Up5YU5semYcPOfwc0/w=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\7yd3quuovy-iovd86k7lj.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-20T15:28:51.7291095+00:00"}, "QiDDOI8VTxpMr5fyC+6JRLebtjnvOPd6fcp8NoIyvZY=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\j89jw65ob7-vr1egmr9el.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-20T15:28:51.7484686+00:00"}, "qlhWSR2l8yRzG5AbSdAikjZBgSg1eq3alHa/DycfvCU=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\ep16rhynv6-kbrnm935zg.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-20T15:28:51.7588959+00:00"}, "Lj4kqcFke+NOnln/WYR0EL5+JJsZRGnvfu6M5B+Rsvs=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\x8dcweb1vd-jj8uyg4cgr.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-20T15:28:51.7618992+00:00"}, "aPFDsVs7yoPGPr0GjyGGAKo/rwdX8w7ky1pAf5k6290=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\9og7ecjao4-y7v9cxd14o.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-20T15:28:51.7706079+00:00"}, "69NMqvEBFkdkzIoLBUWObp3B9Cppud5Sl+q4qdmYxeA=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\p801qb1har-notf2xhcfb.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-20T15:28:51.7757036+00:00"}, "ENAde4ad590yx1rGM+ORKhNgaW6+cMOh7FW8Y46ODls=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\x5ezi2z4uc-h1s4sie4z3.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-20T15:28:51.8277388+00:00"}, "dBa8iCwKi3/abXXwM1+F5RCivdFkA5iSB1RyVJGs2f8=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\afck15avcg-63fj8s7r0e.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-20T15:28:51.8087333+00:00"}, "+n75QEuOt1d2y8s78AO4+ZDzmMjtGVkj0LitB3hylRg=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\9cub2pjme5-0j3bgjxly4.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-20T15:28:51.8147333+00:00"}, "MyL15Fzh+TFTGurulhHa1xvU+l3iEIsoWqmOuKcTUG8=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\43g1cz0sib-47otxtyo56.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-20T15:28:51.8157344+00:00"}, "rVjsDG3D2Witz3BLg1xsOfvpJmJ4xwKCFMizyhn/k5U=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\ywitrqdvij-4v8eqarkd7.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-20T15:28:51.7214414+00:00"}, "GQYANLIM2GnkLMCEwumdaNB0zdY7WinFCkbquI0C1wQ=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\g83bpa2law-356vix0kms.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-20T15:28:51.7224381+00:00"}, "G6sRZB6hiwKjblphMndd6DOIRmqIurHtN2R9f3ydUdk=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\sf6nmr16an-83jwlth58m.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-20T15:28:51.7244378+00:00"}, "XJTUdZxqOXXB8+J/F8zPoWAEAnI94NMk89nELxiksus=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\gm2xl9z3pg-mrlpezrjn3.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-20T15:28:51.7357911+00:00"}, "x4A8UFfLM7phlrOwtApXuLp5LuSE8EsTl2ycxl3gPfs=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\5n5z9tblon-lzl9nlhx6b.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-20T15:28:51.7905168+00:00"}, "JD+zJvuYHiEXKVojzKQkUwXfl/TYKviF266+aTD0/7w=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\n1ry3tj0qt-ag7o75518u.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-20T15:28:51.8370832+00:00"}, "520qSHyJkK6ijCU9Z0TDtB+HqRx74w+Ceu39Xw+vJKI=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\wi0wfjo2bw-x0q3zqp4vz.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-20T15:28:51.8380841+00:00"}, "bFFD15hj+3P0c2v7ePmJTdoOvr6leberxyh1N48dJ+8=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\t24frcjbuy-0i3buxo5is.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-20T15:28:51.8197377+00:00"}, "tmGBrbHSZ566oO4q24S09D3gMPv1EQJ4iV0QhP/ISSI=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\1gwm3zbqks-o1o13a6vjx.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-20T15:28:51.8227391+00:00"}, "03VhJHESqNU36MyAxDiaecctSqPW81PWkyVnpk8+u/U=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\dkeaz84bvx-ttgo8qnofa.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-20T15:28:51.8277388+00:00"}, "h72nnd/PFpr/HLNf8eXx7ORYfQSb2916GK0r6KYAhIU=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\rfdgzsp5zj-2z0ns9nrw6.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-20T15:28:51.7281118+00:00"}, "cwp3SWxSWa0WKpKlakZI+jwi+W9t8TOFO8EgbryS+ZU=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\lqvdmoczh5-muycvpuwrr.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-20T15:28:51.7484686+00:00"}, "lVveZ3MCmXB3pjUEKNiM8F9/1zyb+6jAgPMTA8hbKFg=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\30sr0it263-87fc7y1x7t.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-20T15:28:51.7598968+00:00"}, "LU1dQEyV1gENf6Yh4Y0mxGZ1xcvRstF64J5Os0a/ktg=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\ughi2qvahr-mlv21k5csn.gz", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-20T15:28:51.7676168+00:00"}, "bYKJAcZxIE3vCcg/TfzS9pFF44PhiVli9q10hpDyWuM=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\xft6ogif7q-jg7699auxs.gz", "SourceId": "PetShopWebsite", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "PetShopWebsite#[.{fingerprint=jg7699auxs}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\scopedcss\\bundle\\PetShopWebsite.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hich97ymcv", "Integrity": "mR7/RyyrfYd6LwPoiXTBPZk+mgP1IDEeiq48vTVwj3w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\scopedcss\\bundle\\PetShopWebsite.styles.css", "FileLength": 541, "LastWriteTime": "2025-07-20T15:28:51.7686133+00:00"}, "hokl255loNqZBPYPus/rO2szxZnzk/yvAqn1cTR4Ho8=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\wvgr0vbz7d-jg7699auxs.gz", "SourceId": "PetShopWebsite", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "PetShopWebsite#[.{fingerprint=jg7699auxs}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\PetShopWebsite.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hich97ymcv", "Integrity": "mR7/RyyrfYd6LwPoiXTBPZk+mgP1IDEeiq48vTVwj3w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetShopWebsite\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\PetShopWebsite.bundle.scp.css", "FileLength": 541, "LastWriteTime": "2025-07-20T15:28:51.7686133+00:00"}}, "CachedCopyCandidates": {}}