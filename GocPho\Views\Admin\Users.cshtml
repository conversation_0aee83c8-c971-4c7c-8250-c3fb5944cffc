@model List<GocPho.Models.UserViewModel>
@{
    ViewData["Title"] = "Quản lý người dùng";
    Layout = "_AdminLayout";
}

<div class="row">
    <div class="col-12">
        <div class="card shadow border-0">
            <div class="card-header" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-users me-2"></i>Quản lý người dùng
                    </h5>
                    <span class="badge bg-light text-dark fs-6">@Model.Count người dùng</span>
                </div>
            </div>
            <div class="card-body p-0">
                @if (Model.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th>Tên người dùng</th>
                                    <th>Email</th>
                                    <th>Số điện thoại</th>
                                    <th>Vai trò</th>
                                    <th>Trạng thái</th>
                                    <th class="text-center">Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var user in Model)
                                {
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle me-3">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                                <div>
                                                    <strong>@user.UserName</strong>
                                                    <br>
                                                    <small class="text-muted">ID: @user.Id.Substring(0, 8)...</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                @user.Email
                                                @if (user.EmailConfirmed)
                                                {
                                                    <i class="fas fa-check-circle text-success ms-1" title="Email đã xác thực"></i>
                                                }
                                                else
                                                {
                                                    <i class="fas fa-exclamation-circle text-warning ms-1" title="Email chưa xác thực"></i>
                                                }
                                            </div>
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(user.PhoneNumber))
                                            {
                                                <span>@user.PhoneNumber</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">Chưa cập nhật</span>
                                            }
                                        </td>
                                        <td>
                                            @if (user.Roles.Any())
                                            {
                                                @foreach (var role in user.Roles)
                                                {
                                                    <span class="badge @(role == "Admin" ? "bg-danger" : "bg-primary") me-1">
                                                        @switch (role)
                                                        {
                                                            case "Admin":
                                                                <text>Quản trị viên</text>
                                                                break;
                                                            case "Customer":
                                                                <text>Khách hàng</text>
                                                                break;
                                                            default:
                                                                @role
                                                                break;
                                                        }
                                                    </span>
                                                }
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">Chưa phân quyền</span>
                                            }
                                        </td>
                                        <td>
                                            @if (user.EmailConfirmed)
                                            {
                                                <span class="badge bg-success">Hoạt động</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-warning text-dark">Chờ xác thực</span>
                                            }
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-info" title="Xem chi tiết">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                @if (!user.Roles.Contains("Admin"))
                                                {
                                                    <button class="btn btn-sm btn-outline-warning" title="Chỉnh sửa">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="Vô hiệu hóa">
                                                        <i class="fas fa-ban"></i>
                                                    </button>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Không có người dùng nào</h5>
                        <p class="text-muted">Chưa có người dùng nào được tạo trong hệ thống.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stats-icon bg-primary text-white rounded-circle mx-auto mb-3">
                    <i class="fas fa-users"></i>
                </div>
                <h4 class="fw-bold">@Model.Count</h4>
                <p class="text-muted mb-0">Tổng người dùng</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stats-icon bg-danger text-white rounded-circle mx-auto mb-3">
                    <i class="fas fa-user-shield"></i>
                </div>
                <h4 class="fw-bold">@Model.Count(u => u.Roles.Contains("Admin"))</h4>
                <p class="text-muted mb-0">Quản trị viên</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stats-icon bg-info text-white rounded-circle mx-auto mb-3">
                    <i class="fas fa-user"></i>
                </div>
                <h4 class="fw-bold">@Model.Count(u => u.Roles.Contains("Customer"))</h4>
                <p class="text-muted mb-0">Khách hàng</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stats-icon bg-success text-white rounded-circle mx-auto mb-3">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h4 class="fw-bold">@Model.Count(u => u.EmailConfirmed)</h4>
                <p class="text-muted mb-0">Đã xác thực</p>
            </div>
        </div>
    </div>
</div>

<style>
    .avatar-circle {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 16px;
    }
    
    .stats-icon {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }
</style>
