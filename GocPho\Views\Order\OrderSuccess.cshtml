@model GocPho.Models.Order
@{
    ViewData["Title"] = "Đặt hàng thành công";
}

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Success Message -->
            <div class="text-center mb-5">
                <div class="mb-4">
                    <i class="fas fa-check-circle fa-5x text-success"></i>
                </div>
                <h1 class="display-5 fw-bold text-success">Đặt hàng thành công!</h1>
                <p class="lead text-muted">Cảm ơn bạn đã đặt hàng tại Góc phố Coffee</p>
            </div>

            <!-- Order Details -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-receipt me-2"></i>Chi tiết đơn hàng #@Model.Id
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6><i class="fas fa-user text-warning me-2"></i>Thông tin khách hàng</h6>
                            <p class="mb-1"><strong>Họ tên:</strong> @Model.CustomerName</p>
                            <p class="mb-1"><strong>Số điện thoại:</strong> @Model.PhoneNumber</p>
                            <p class="mb-0"><strong>Địa chỉ:</strong> @Model.DeliveryAddress</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle text-warning me-2"></i>Thông tin đơn hàng</h6>
                            <p class="mb-1"><strong>Ngày đặt:</strong> @Model.OrderDate.ToString("dd/MM/yyyy HH:mm")</p>
                            <p class="mb-1"><strong>Trạng thái:</strong> 
                                <span class="badge bg-warning text-dark">Chờ xác nhận</span>
                            </p>
                            <p class="mb-0"><strong>Tổng tiền:</strong> 
                                <span class="text-success fw-bold">@Model.TotalAmount.ToString("N0") VNĐ</span>
                            </p>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Notes))
                    {
                        <div class="mb-4">
                            <h6><i class="fas fa-sticky-note text-warning me-2"></i>Ghi chú</h6>
                            <p class="text-muted">@Model.Notes</p>
                        </div>
                    }

                    <!-- Order Items -->
                    <h6><i class="fas fa-list text-warning me-2"></i>Sản phẩm đã đặt</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead class="bg-light">
                                <tr>
                                    <th>Sản phẩm</th>
                                    <th class="text-center">Số lượng</th>
                                    <th class="text-end">Đơn giá</th>
                                    <th class="text-end">Thành tiền</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.OrderItems)
                                {
                                    <tr>
                                        <td>@item.Product?.Name</td>
                                        <td class="text-center">@item.Quantity</td>
                                        <td class="text-end">@item.UnitPrice.ToString("N0") VNĐ</td>
                                        <td class="text-end fw-bold">@item.TotalPrice.ToString("N0") VNĐ</td>
                                    </tr>
                                }
                            </tbody>
                            <tfoot class="bg-light">
                                <tr>
                                    <th colspan="3">Tổng cộng:</th>
                                    <th class="text-end text-success">@Model.TotalAmount.ToString("N0") VNĐ</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Các bước tiếp theo
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center mb-3">
                            <div class="mb-2">
                                <i class="fas fa-phone fa-2x text-warning"></i>
                            </div>
                            <h6>1. Xác nhận đơn hàng</h6>
                            <small class="text-muted">Chúng tôi sẽ gọi điện xác nhận trong 5-10 phút</small>
                        </div>
                        <div class="col-md-4 text-center mb-3">
                            <div class="mb-2">
                                <i class="fas fa-blender fa-2x text-warning"></i>
                            </div>
                            <h6>2. Chuẩn bị đơn hàng</h6>
                            <small class="text-muted">Pha chế cà phê tươi ngon cho bạn</small>
                        </div>
                        <div class="col-md-4 text-center mb-3">
                            <div class="mb-2">
                                <i class="fas fa-shipping-fast fa-2x text-warning"></i>
                            </div>
                            <h6>3. Giao hàng</h6>
                            <small class="text-muted">Giao hàng trong 30-60 phút</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="text-center">
                <div class="d-flex flex-wrap justify-content-center gap-3">
                    <a href="@Url.Action("MyOrders", "Order")" class="btn btn-warning">
                        <i class="fas fa-list me-2"></i>Xem đơn hàng của tôi
                    </a>
                    <a href="@Url.Action("Menu", "Product")" class="btn btn-outline-warning">
                        <i class="fas fa-coffee me-2"></i>Tiếp tục mua sắm
                    </a>
                    <a href="@Url.Action("Index", "Home")" class="btn btn-outline-secondary">
                        <i class="fas fa-home me-2"></i>Về trang chủ
                    </a>
                </div>
            </div>

            <!-- Contact Info -->
            <div class="alert alert-info mt-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-1">
                            <i class="fas fa-headset me-2"></i>Cần hỗ trợ?
                        </h6>
                        <small>Liên hệ với chúng tôi nếu bạn có bất kỳ thắc mắc nào về đơn hàng</small>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="tel:0123456789" class="btn btn-sm btn-outline-info">
                            <i class="fas fa-phone me-1"></i>0123 456 789
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
