@model GocPho.Models.Product
@{
    ViewData["Title"] = Model.Name;
}

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Trang chủ</a></li>
                    <li class="breadcrumb-item"><a href="@Url.Action("Menu", "Product")">Menu</a></li>
                    <li class="breadcrumb-item active">@Model.Name</li>
                </ol>
            </nav>

            <div class="row">
                <!-- Product Image -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow-sm border-0">
                        <img src="@Model.ImageUrl" alt="@Model.Name" class="card-img-top" 
                             style="height: 400px; object-fit: cover;"
                             onerror="this.src='/images/default-coffee.svg'">
                    </div>
                </div>

                <!-- Product Info -->
                <div class="col-lg-6">
                    <div class="card shadow-sm border-0 h-100">
                        <div class="card-body">
                            <div class="mb-3">
                                <span class="badge bg-warning text-dark mb-2">@Model.Category?.Name</span>
                                <h1 class="display-6 fw-bold">@Model.Name</h1>
                            </div>

                            <div class="mb-4">
                                <h2 class="text-warning fw-bold">@Model.Price.ToString("N0") VNĐ</h2>
                            </div>

                            <div class="mb-4">
                                <h5>Mô tả sản phẩm</h5>
                                <p class="text-muted">@Model.Description</p>
                            </div>

                            <div class="mb-4">
                                @if (Model.IsAvailable)
                                {
                                    <span class="badge bg-success fs-6">
                                        <i class="fas fa-check me-1"></i>Có sẵn
                                    </span>
                                }
                                else
                                {
                                    <span class="badge bg-danger fs-6">
                                        <i class="fas fa-times me-1"></i>Hết hàng
                                    </span>
                                }
                            </div>

                            @if (Model.IsAvailable)
                            {
                                @if (User.Identity?.IsAuthenticated == true)
                                {
                                    <div class="mb-4">
                                        <div class="row align-items-center">
                                            <div class="col-md-4">
                                                <label class="form-label">Số lượng:</label>
                                                <div class="input-group">
                                                    <button class="btn btn-outline-secondary" type="button" id="decreaseBtn">
                                                        <i class="fas fa-minus"></i>
                                                    </button>
                                                    <input type="number" class="form-control text-center" id="quantity" value="1" min="1" max="10">
                                                    <button class="btn btn-outline-secondary" type="button" id="increaseBtn">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="col-md-8">
                                                <button class="btn btn-warning btn-lg w-100" id="addToCartBtn" data-product-id="@Model.Id">
                                                    <i class="fas fa-cart-plus me-2"></i>Thêm vào giỏ hàng
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    <div class="mb-4">
                                        <a href="@Url.Action("Login", "Account")" class="btn btn-warning btn-lg w-100">
                                            <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập để mua hàng
                                        </a>
                                    </div>
                                }
                            }

                            <div class="d-flex gap-2">
                                <a href="@Url.Action("Menu", "Product")" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Quay lại menu
                                </a>
                                <a href="@Url.Action("Menu", "Product", new { categoryId = Model.CategoryId })" class="btn btn-outline-warning">
                                    <i class="fas fa-list me-2"></i>Cùng danh mục
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Product Features -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="fas fa-star text-warning me-2"></i>Đặc điểm nổi bật
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 text-center mb-3">
                                    <i class="fas fa-leaf fa-2x text-success mb-2"></i>
                                    <h6>Nguyên liệu tự nhiên</h6>
                                    <small class="text-muted">Cà phê được chọn lọc từ những hạt cà phê chất lượng cao</small>
                                </div>
                                <div class="col-md-4 text-center mb-3">
                                    <i class="fas fa-fire fa-2x text-danger mb-2"></i>
                                    <h6>Pha chế tươi mới</h6>
                                    <small class="text-muted">Mỗi ly cà phê được pha chế ngay khi có đơn hàng</small>
                                </div>
                                <div class="col-md-4 text-center mb-3">
                                    <i class="fas fa-shipping-fast fa-2x text-warning mb-2"></i>
                                    <h6>Giao hàng nhanh</h6>
                                    <small class="text-muted">Giao hàng trong vòng 30-60 phút</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Quantity controls
            $('#increaseBtn').click(function() {
                var quantity = parseInt($('#quantity').val());
                if (quantity < 10) {
                    $('#quantity').val(quantity + 1);
                }
            });

            $('#decreaseBtn').click(function() {
                var quantity = parseInt($('#quantity').val());
                if (quantity > 1) {
                    $('#quantity').val(quantity - 1);
                }
            });

            // Add to cart
            $('#addToCartBtn').click(function() {
                var productId = $(this).data('product-id');
                var quantity = parseInt($('#quantity').val());
                var button = $(this);
                
                button.prop('disabled', true);
                button.html('<i class="fas fa-spinner fa-spin me-2"></i>Đang thêm...');
                
                $.post('/Cart/AddToCart', { productId: productId, quantity: quantity })
                    .done(function(response) {
                        if (response.success) {
                            showToast('success', response.message);
                            if (window.updateCartCount) {
                                window.updateCartCount(response.cartCount);
                            }
                            // Reset quantity
                            $('#quantity').val(1);
                        } else {
                            showToast('error', response.message);
                        }
                    })
                    .fail(function() {
                        showToast('error', 'Có lỗi xảy ra. Vui lòng thử lại.');
                    })
                    .always(function() {
                        button.prop('disabled', false);
                        button.html('<i class="fas fa-cart-plus me-2"></i>Thêm vào giỏ hàng');
                    });
            });
        });

        function showToast(type, message) {
            var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            var icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
            
            var toast = $('<div class="alert ' + alertClass + ' alert-dismissible fade show position-fixed" style="top: 100px; right: 20px; z-index: 9999;">' +
                '<i class="fas ' + icon + ' me-2"></i>' + message +
                '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                '</div>');
            
            $('body').append(toast);
            
            setTimeout(function() {
                toast.alert('close');
            }, 3000);
        }
    </script>
}
