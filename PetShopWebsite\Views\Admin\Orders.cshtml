@model List<PetShopWebsite.Models.Order>
@{
    ViewData["Title"] = "Quản Lý Đơn Hàng";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">Quản Lý Đơn Hàng</h1>
    <div class="d-flex">
        <div class="dropdown mr-2">
            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-toggle="dropdown">
                <i class="fas fa-filter"></i> L<PERSON><PERSON> theo trạng thái
            </button>
            <div class="dropdown-menu">
                <a class="dropdown-item" href="@Url.Action("Orders", new { status = "" })">Tất cả</a>
                <a class="dropdown-item" href="@Url.Action("Orders", new { status = "Pending" })">Ch<PERSON> x<PERSON><PERSON> nhậ<PERSON></a>
                <a class="dropdown-item" href="@Url.Action("Orders", new { status = "Confirmed" })">Đ<PERSON> xác nhận</a>
                <a class="dropdown-item" href="@Url.Action("Orders", new { status = "Processing" })">Đang xử lý</a>
                <a class="dropdown-item" href="@Url.Action("Orders", new { status = "Shipping" })">Đang giao</a>
                <a class="dropdown-item" href="@Url.Action("Orders", new { status = "Delivered" })">Đã giao</a>
                <a class="dropdown-item" href="@Url.Action("Orders", new { status = "Cancelled" })">Đã hủy</a>
            </div>
        </div>
    </div>
</div>

<!-- Search Form -->
<div class="card shadow mb-4">
    <div class="card-body">
        <form method="get" class="row">
            <div class="col-md-4">
                <input type="text" name="search" class="form-control" placeholder="Tìm kiếm theo mã đơn, tên khách hàng..." value="@ViewBag.Search">
            </div>
            <div class="col-md-3">
                <select name="status" class="form-control">
                    <option value="">Tất cả trạng thái</option>
                    <option value="Pending" selected="@(ViewBag.Status?.ToString() == "Pending")">Chờ xác nhận</option>
                    <option value="Confirmed" selected="@(ViewBag.Status?.ToString() == "Confirmed")">Đã xác nhận</option>
                    <option value="Processing" selected="@(ViewBag.Status?.ToString() == "Processing")">Đang xử lý</option>
                    <option value="Shipping" selected="@(ViewBag.Status?.ToString() == "Shipping")">Đang giao</option>
                    <option value="Delivered" selected="@(ViewBag.Status?.ToString() == "Delivered")">Đã giao</option>
                    <option value="Cancelled" selected="@(ViewBag.Status?.ToString() == "Cancelled")">Đã hủy</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Tìm kiếm
                </button>
            </div>
        </form>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Danh Sách Đơn Hàng</h6>
    </div>
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Mã Đơn Hàng</th>
                            <th>Khách Hàng</th>
                            <th>Ngày Đặt</th>
                            <th>Tổng Tiền</th>
                            <th>Trạng Thái</th>
                            <th>Thanh Toán</th>
                            <th>Thao Tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var order in Model)
                        {
                            <tr>
                                <td>
                                    <strong>#@order.OrderNumber</strong>
                                </td>
                                <td>
                                    <div>
                                        <strong>@order.ShippingName</strong><br>
                                        <small class="text-muted">@order.ShippingPhone</small>
                                    </div>
                                </td>
                                <td>@order.OrderDate.ToString("dd/MM/yyyy HH:mm")</td>
                                <td>
                                    <strong>@order.TotalAmount.ToString("N0") ₫</strong>
                                </td>
                                <td>
                                    <span class="badge 
                                        @(order.Status == PetShopWebsite.Models.OrderStatus.Pending ? "badge-warning" :
                                          order.Status == PetShopWebsite.Models.OrderStatus.Confirmed ? "badge-info" :
                                          order.Status == PetShopWebsite.Models.OrderStatus.Processing ? "badge-primary" :
                                          order.Status == PetShopWebsite.Models.OrderStatus.Shipping ? "badge-secondary" :
                                          order.Status == PetShopWebsite.Models.OrderStatus.Delivered ? "badge-success" :
                                          "badge-danger")">
                                        @(order.Status == PetShopWebsite.Models.OrderStatus.Pending ? "Chờ xác nhận" :
                                          order.Status == PetShopWebsite.Models.OrderStatus.Confirmed ? "Đã xác nhận" :
                                          order.Status == PetShopWebsite.Models.OrderStatus.Processing ? "Đang xử lý" :
                                          order.Status == PetShopWebsite.Models.OrderStatus.Shipping ? "Đang giao" :
                                          order.Status == PetShopWebsite.Models.OrderStatus.Delivered ? "Đã giao" :
                                          "Đã hủy")
                                    </span>
                                </td>
                                <td>
                                    <span class="badge 
                                        @(order.PaymentStatus == PetShopWebsite.Models.PaymentStatus.Pending ? "badge-warning" :
                                          order.PaymentStatus == PetShopWebsite.Models.PaymentStatus.Paid ? "badge-success" :
                                          "badge-danger")">
                                        @(order.PaymentStatus == PetShopWebsite.Models.PaymentStatus.Pending ? "Chờ thanh toán" :
                                          order.PaymentStatus == PetShopWebsite.Models.PaymentStatus.Paid ? "Đã thanh toán" :
                                          "Thất bại")
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewOrderDetails(@order.Id)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        @if (order.Status == PetShopWebsite.Models.OrderStatus.Pending)
                                        {
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="confirmOrder(@order.Id)">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        }
                                        @if (order.Status != PetShopWebsite.Models.OrderStatus.Delivered && order.Status != PetShopWebsite.Models.OrderStatus.Cancelled)
                                        {
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="cancelOrder(@order.Id)">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if (ViewBag.TotalPages > 1)
            {
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        @for (int i = 1; i <= ViewBag.TotalPages; i++)
                        {
                            <li class="page-item @(i == ViewBag.CurrentPage ? "active" : "")">
                                <a class="page-link" href="@Url.Action("Orders", new { page = i, search = ViewBag.Search, status = ViewBag.Status })">@i</a>
                            </li>
                        }
                    </ul>
                </nav>
            }
        }
        else
        {
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Không tìm thấy đơn hàng nào</h5>
                <p class="text-muted">Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm.</p>
            </div>
        }
    </div>
</div>

<!-- Order Details Modal -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Chi Tiết Đơn Hàng</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="orderDetailsContent">
                <!-- Order details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function viewOrderDetails(orderId) {
            // TODO: Load order details via AJAX
            $('#orderDetailsContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Đang tải...</div>');
            $('#orderDetailsModal').modal('show');
            
            // Simulate loading
            setTimeout(function() {
                $('#orderDetailsContent').html('<p>Chi tiết đơn hàng #' + orderId + ' sẽ được hiển thị ở đây.</p>');
            }, 1000);
        }

        function confirmOrder(orderId) {
            if (confirm('Bạn có chắc chắn muốn xác nhận đơn hàng này?')) {
                // TODO: Implement confirm order functionality
                alert('Chức năng xác nhận đơn hàng sẽ được triển khai sau');
            }
        }

        function cancelOrder(orderId) {
            if (confirm('Bạn có chắc chắn muốn hủy đơn hàng này?')) {
                // TODO: Implement cancel order functionality
                alert('Chức năng hủy đơn hàng sẽ được triển khai sau');
            }
        }
    </script>
}
