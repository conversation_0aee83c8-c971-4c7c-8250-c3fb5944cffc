@model GocPho.Models.AdminDashboardViewModel
@{
    ViewData["Title"] = "Dashboard Quản trị";
    Layout = "_AdminLayout";
}

<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
            <div class="card-body text-white">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="fw-bold mb-2">
                            <i class="fas fa-coffee me-2"></i>Chào mừng đến với Góc phố Coffee Admin
                        </h2>
                        <p class="mb-0 opacity-75">Quản lý cửa hàng coffee của bạn một cách hiệu quả</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <i class="fas fa-chart-line fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row g-4 mb-5">
    <div class="col-xl-3 col-md-6">
        <div class="card stats-card-warning shadow border-0 h-100">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-2 opacity-75">Tổng sản phẩm</h6>
                        <h2 class="fw-bold mb-0">@Model.TotalProducts</h2>
                        <small class="opacity-75">Sản phẩm đang bán</small>
                    </div>
                    <div>
                        <i class="fas fa-coffee fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card stats-card-info shadow border-0 h-100">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-2 opacity-75">Tổng đơn hàng</h6>
                        <h2 class="fw-bold mb-0">@Model.TotalOrders</h2>
                        <small class="opacity-75">Đơn hàng đã tạo</small>
                    </div>
                    <div>
                        <i class="fas fa-shopping-cart fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card stats-card-warning shadow border-0 h-100">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-2 opacity-75">Đơn hàng chờ</h6>
                        <h2 class="fw-bold mb-0">@Model.PendingOrders</h2>
                        <small class="opacity-75">Cần xử lý</small>
                    </div>
                    <div>
                        <i class="fas fa-clock fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card stats-card-success shadow border-0 h-100">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-2 opacity-75">Doanh thu</h6>
                        <h2 class="fw-bold mb-0">@Model.TotalRevenue.ToString("N0")</h2>
                        <small class="opacity-75">VNĐ</small>
                    </div>
                    <div>
                        <i class="fas fa-chart-line fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row g-4 mb-5">
    <div class="col-12">
        <div class="card shadow border-0">
            <div class="card-header" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
                <h5 class="mb-0 text-white">
                    <i class="fas fa-bolt me-2"></i>Thao tác nhanh
                </h5>
            </div>
            <div class="card-body p-4">
                <div class="row g-3">
                    <div class="col-lg-3 col-md-6">
                        <a href="@Url.Action("CreateProduct", "Admin")" class="btn btn-warning w-100 py-3 shadow-sm">
                            <i class="fas fa-plus-circle fa-lg me-2"></i>
                            <div>Thêm sản phẩm</div>
                            <small class="opacity-75">Tạo sản phẩm mới</small>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="@Url.Action("Products", "Admin")" class="btn btn-info w-100 py-3 shadow-sm">
                            <i class="fas fa-coffee fa-lg me-2"></i>
                            <div>Quản lý sản phẩm</div>
                            <small class="opacity-75">Xem tất cả sản phẩm</small>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="@Url.Action("Orders", "Admin")" class="btn btn-success w-100 py-3 shadow-sm">
                            <i class="fas fa-shopping-cart fa-lg me-2"></i>
                            <div>Quản lý đơn hàng</div>
                            <small class="opacity-75">Xem tất cả đơn hàng</small>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="@Url.Action("Orders", "Admin", new { status = GocPho.Models.OrderStatus.Pending })"
                           class="btn btn-danger w-100 py-3 shadow-sm">
                            <i class="fas fa-exclamation-triangle fa-lg me-2"></i>
                            <div>Đơn hàng chờ</div>
                            <small class="opacity-75">Cần xử lý ngay</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Orders -->
<div class="row">
    <div class="col-12">
        <div class="card shadow border-0">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2 text-warning"></i>Đơn hàng gần đây
                    </h5>
                    <a href="@Url.Action("Orders", "Admin")" class="btn btn-sm btn-warning">
                        <i class="fas fa-eye me-1"></i>Xem tất cả
                    </a>
                </div>
            </div>
                        <div class="card-body p-0">
                            @if (Model.RecentOrders.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="bg-light">
                                            <tr>
                                                <th>Mã đơn</th>
                                                <th>Khách hàng</th>
                                                <th>Ngày đặt</th>
                                                <th>Tổng tiền</th>
                                                <th>Trạng thái</th>
                                                <th class="text-center">Thao tác</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var order in Model.RecentOrders)
                                            {
                                                <tr>
                                                    <td class="fw-bold">#@order.Id</td>
                                                    <td>@order.CustomerName</td>
                                                    <td>@order.OrderDate.ToString("dd/MM/yyyy HH:mm")</td>
                                                    <td class="fw-bold">@order.TotalAmount.ToString("N0") VNĐ</td>
                                                    <td>
                                                        <span class="badge <EMAIL>().ToLower()">
                                                            @switch (order.Status)
                                                            {
                                                                case GocPho.Models.OrderStatus.Pending:
                                                                    <text>Chờ xác nhận</text>
                                                                    break;
                                                                case GocPho.Models.OrderStatus.Confirmed:
                                                                    <text>Đã xác nhận</text>
                                                                    break;
                                                                case GocPho.Models.OrderStatus.Preparing:
                                                                    <text>Đang chuẩn bị</text>
                                                                    break;
                                                                case GocPho.Models.OrderStatus.Delivering:
                                                                    <text>Đang giao hàng</text>
                                                                    break;
                                                                case GocPho.Models.OrderStatus.Delivered:
                                                                    <text>Đã giao hàng</text>
                                                                    break;
                                                                case GocPho.Models.OrderStatus.Cancelled:
                                                                    <text>Đã hủy</text>
                                                                    break;
                                                            }
                                                        </span>
                                                    </td>
                                                    <td class="text-center">
                                                        <a href="@Url.Action("OrderDetails", "Admin", new { id = order.Id })" 
                                                           class="btn btn-sm btn-outline-warning">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">Chưa có đơn hàng nào</p>
                                </div>
                            }
            </div>
        </div>
    </div>
</div>
