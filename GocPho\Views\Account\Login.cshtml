@model GocPho.Models.LoginViewModel
@{
    ViewData["Title"] = "Đăng nhập";
}

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm border-0">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-coffee fa-3x text-warning mb-3"></i>
                        <h2 class="fw-bold">Đăng nhập</h2>
                        <p class="text-muted">Chào mừng bạn quay trở lại Góc phố Coffee</p>
                    </div>

                    <form asp-action="Login" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="mb-3">
                            <label asp-for="Email" class="form-label">Email</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input asp-for="Email" class="form-control" placeholder="Nhập email của bạn" />
                            </div>
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Password" class="form-label">Mật khẩu</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input asp-for="Password" class="form-control" placeholder="Nhập mật khẩu" />
                            </div>
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="mb-3 form-check">
                            <input asp-for="RememberMe" class="form-check-input" />
                            <label asp-for="RememberMe" class="form-check-label">
                                Ghi nhớ đăng nhập
                            </label>
                        </div>

                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-warning btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập
                            </button>
                        </div>

                        <div class="text-center">
                            <p class="mb-0">Chưa có tài khoản? 
                                <a asp-action="Register" class="text-warning text-decoration-none fw-bold">Đăng ký ngay</a>
                            </p>
                        </div>
                    </form>

                    <hr class="my-4">
                    
                    <div class="text-center">
                        <small class="text-muted">
                            <strong>Tài khoản demo:</strong><br>
                            Admin: <EMAIL> / admin@123<br>
                            Hoặc đăng ký tài khoản mới để trải nghiệm
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
