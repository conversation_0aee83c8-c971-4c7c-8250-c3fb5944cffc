{"GlobalPropertiesHash": "AH4hMTdxLCCM3IYRuMlZ5w+o3AsHD4lsfv+4zezKVNU=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["8WQUjZ1BO2rUfub9FNKR9AHr6cZyCoy/YQhBHTuJHug=", "3iNZutqUgC/5Ztiuhzn79RWurOVEJOowQl4/x9p56Gk=", "Lx2jyWox6EXVyZMSWH6P26jdPwSJ3teT/TytNSTytGU=", "7VgZl7pDWg/LBTp3sxaYrbVQaOMAwFMdLgqcK8m8mnc=", "0jwOkv4c42bsSBsdFIJG79uHHaHLY05axhTWUR9UcUY=", "G4aLXXC7/ad40VwtcCe1CgAnO8gfbj/SJf0BUmo1ZBA=", "mg2NWLcKjUzDQrVR1vYlKkP6xpTse2TdPJPlpSZpzt0=", "KAe0X0KUuu5ZOS/CAbTRYBqBHXtDp26sSGGHW6G/Hgo=", "4+JatPv/Lmlqw4V5IrAITqkR1mln2GB1kg1GJK2TvLs=", "BHiqUQAV6qLsQjIgIKo1hSvN8dtGTlAmjkybDtWMH54=", "euttKNsKWdyn4Vsi76SNP0tGEyoryZa1eG2uXYO1ed8=", "Jd44u405isrwsJNP98BgiupuwzzGgvD2611dLE+ESZ4=", "9dLTZBfyRLUcJ3BR2XhibGQ6ZiOwO0cuwfAADk1S9pc=", "i4/vOfST9KKxmY9kOcMkxjZDEmHZN65AKJxSlV/NzZg=", "t7qs7CtyibgsnH+tJlAeG6KeL9m9/om4nIVMFkMt6n0=", "Sw6Wsj8coSHx8tZybDktDeVG1vtzRASiOS/yA5XtYRs=", "cPaMyfSgWkIq0V7Zcw03SDLH2fqoK0pNZLVEE58uRzk=", "ihi5boac1hCXmA6wxAgcsdB9SPWN5BeVhsujvdmVTPo=", "dEv7yqJeTBQAZIy4vsNg7gJFd+HnjGVisDjVmhQ1H44=", "yCM3pds01Bc3yc9puSr/h4TvuOL6VAak1EL0gRORcb0=", "nZAHo1pghM41QZMkJWaaBtpVS3/CNeS8QiRCBbwW/2U=", "Evzat+vVwf3c7b1Hx3BeVSMgvh3dl8nzkMb5CK5BrXE=", "dpqpgr3jf8Odldtd4QoUufXPZ1KslPN8mokkQTsViwA=", "OOZJ0Hqmeda70PF6YG/J+atKFYJpvz2GdRV9NYuUO9k=", "zIoyNujraGRthZxIjGjfTBAaTOsyTzbLdq0RqZDvcwY=", "vnThyoVIRgshoUA4FkUTsMndGOCk7vQAMH8/13uOOEI=", "kBgI4m63Qd/BpZ0uORiFptugbUotFiiFZKCJHs0t2Hg=", "5ab4iAVehp/uFYcesrzgPg8e4G4xdgCh41YSE8kgkDs=", "uhK+W2yOfvFUAoho2VkUBZ1sE9m3tpxMLoJnc4YZsFM=", "N5OZiLx1uzOJDBLHLfPxg1QttzpvZVSi/jnILISAU6o=", "rYBpPIFb6oaJIE31ATlPApfWj8ys/kCIVwEWTuE578Q=", "gGCtzogOEKD+r6DK63AxUkAcxjqIID6Gh4NDKhYq32Y=", "j4YVZNlXyf1FJOiC9XUhBVjQM1+9FWJLW7Ey6M8mv0c=", "SwxE/YbFoWLCnkp55dsriOnJuulGgLk9ahbzmaaBNWE=", "noPBsG4wd8YquzCpp02lsu4tyt91ggIwMusNHIicshs=", "4wfC5Ky/Hz9QOhfTfJpDIRadcl8Xq6syjS587kkI+kM=", "puCuW5xgHyHhxDh/NOvArOGbKpkIsT/dbObcLSJUsX8=", "hQcYX9HFngViS3XOcDopewpem3gyGhJ41tUW+V0PdTY=", "wLYufn8EYjKL1Y9hGOgLstip3AHwVo7gbjbE9SK+U1A=", "9WI7r/eZIMQgHecahIr0tKI1gHFZtkMSo87gWZ8BfsY=", "eHmiz6hkaLmH1hbiWjKe5LZEswoheaO1Fgy7hobVLpM=", "h5hSU4BqxG2F9cBj5tBoZOeHQ6jGvIIKQZueSPjhtb8=", "1US+xPJmzpeL2JBVFruD4ktL1s+1uVhwWdNWqMJW1pU=", "hFmsMKORRt46dF1GCmIHNv0K5ANlzibyTs03gmhAv9o=", "t21o1ELDXvtzuhXhEpNFqTZZHiMdriolNwDO5i4XfwA=", "PL549ddgzpqqZXLTYeS2uASwjqFAnyyjzwpsjN1dU/w=", "fNZhiVpwczA7C9qtt+s6zy/oti9ZSx28D1SkqIxTFU0=", "6EEONtOVF8ldiQYRs4MQ3kzTiULM9bfB5M2S0GUZZNI=", "PIP10+IodJtXnU+zFEOVx1Gzc8PWxolsvpTvAD7/LUw=", "Alc8LNxbR8lBoDrOkT1g/MHR35uVQ+uvHyIqYQKCTe8=", "AgkVT0oSDPTzHLgmwdxcznQV6DZThitVMMgNz/P66a4=", "mS9ek/tVHLV/jH3l7Ru0sePw32DC8Bzy/V7XE+ByoBw=", "7cgRYtNPwY+Uw8hzAHlwViAnA/9acbhyfWP8eq4QUHU=", "HB/FNWIpyIqWFzgNQoatHcwu1ADeJmmy2gO83lncFzM=", "fu4fssapi9iepp7mDoyOunL7/JMFev3Dc8Hk5X5UJpU=", "D+uMMLz6lQbzIRPfP5tp89BNyy5k6nt8hWmiGTFMkPk=", "YfJAoHTHDEwLlEtjbxJaqZixq75A6w2uL1Acvgczx2Q=", "Ik273Strot8pUB6yOPZOKela9NRxsCxvSVyzwJbWScE=", "3+WWaIG9yj7ZhiCi+G3PX5qhRXePYD12YUCpNKjIacs=", "/N3hjzyUspmc9wxMQTsXpzZi3tz5dIO9VyDIa3O1Ed8=", "sLkCqpOtDtgKTg0wvMsGqYLCVUcWO7lGGs5eV01T7N8=", "1IfwesSS2Y2+twR2ru5y2peCpWwRdqiIRjatsFpki4A=", "pKRHWf9S/RJ8P56VUE+QAg/ID9DHpQYyVkym6ZNoCXg=", "DkicDRvKC2A4L6MryHmOfl6cLRVCnyz/utvSFQ4Y2n4=", "q0avzGOw+l1rPLXyDBEQOfco8BWm6xTep2gKFwvgIHs=", "7Soguoeym/419d62t9gxvGs2HV481eZQq3HjOKI3r3M=", "yD0lUxCtqKqcPZE30/nw3ItS6wLbwbz+a+BAWhPbIe0=", "qUVmwm0oCvy95IE3eBHj+IVUHbekYSdnBu/5Btxj3vw=", "KyU9aYgagrKACS2q44B/cxdHncNTh6GY2h2LDryOGq4=", "A+kXQTTTHdUuwslMG32LcW0iIyhOxy8QGuU9JJiF6x8=", "oYEUhsAzLCNwams55j1Z7fnhC+Wd4RjJse2caj4G2eU=", "HU043jf8GTYssKQ50ojYQu0nKdHedWL7B9XNAduo3r0=", "18EnTGxATyMnJ9A3ZrjRDwhs8lwbZIokSWc6PYOTXzw=", "UnYehbD+lniChm/1840AuJk3nBxKD3MGTO8t/FVzUfM=", "UVJkGRCH06LqSgm9KJ9PFLEJCY0ky48cwmDrdznRd/I=", "2xTRCvKmXkoG7TM/CHV9JCvIBZGD3ZDF9ZiMAAM6X4c=", "BOKjdOnljW6LQSSlb6i3rRGyj7Li8traT9F485MRobc=", "PjIRnz0y0OlCUxAQb7O9EVXz3EppBmFSSvc/Fwm4YNY=", "XMCSjNV0czdIR21WWKhGpY6F8Ws+wSRfR+EsMn9GtEc=", "/crMSfZenzuBWgmqfkFz8Kij3cIbBE2GQvzi87Q3W3U=", "qyEIhY+8p0AkZ4ZMGKPKJIyfQwnaZGQu3o9TXXJkXVE=", "cczqlP/scjkg/XrghgEylZUaTT8WExyHMDkrJfvTe30=", "NmmEGwTzZpaUDDaHrNQJIw8r8w9FBVTenf6y8/YZ+xU=", "sI50mFCBrbXjt319IQKf3tmKdwV7ObY1+/IQVubVwkE=", "vnXr9WxFP50Z54vbSofknx2v9QW2ar7W5CfKXHEjiNg=", "vAz7f4J1Zx1XA/bw8+c9PFzhXqBkx+NaUQsbhz1F0Xg=", "7gGRzuXAvWpsGBQpKEkWZMe0jc7FMpKkWBvdKn/CN0g=", "YHhH+tVz3rn7eYvE7LT5rzPNBM3bcGZZLVa3lje6ZKw=", "3Nc4pv8SJDwUWI+cVmBs8u2bbbO7FpW+LjC1f7MNE7k=", "EkOPrv87WhvHIHvYxZMyKwbFpORhTxA+FM7iIE0ZZyE=", "AlBHeygxZya9NLjjaZVg5z0LPdH8FYWUAI8OZrBc000=", "esdQ6vxUAYEZHPx0XBY4i+HD9wG1cjwoyoHtlNyW+CI=", "gjQBvDWt2ZiOy05sAMgwYDssmacJS1uuLZ6NxUEm++4=", "W6mFqnnTL12JB4WBXvtG2AiBUy++rMGapXoFNEM0H4A=", "VhSZoMs5jCBmbo/CDs5Exrcpj2ykKIZ3BG85YVivpm8=", "SDTsEr5wt2JOob6s5TkPkLxUbls9OIp+Lg70b5L0Jv8=", "5G5C8r9vxfLkOHrC+d8OtO5QhFSPCeLAAFwUW3STm1g=", "/X7QSaW7iQXPeyHOLthJ/fIXNDBELdwL7H4ZwZhtzBY=", "vdoae00+IpjLBYN0Y5JuIUAG9StySUitoUa+HQAacWQ=", "TBsyeskCQsQyCdNAQptGPFSC0IK7epPzNMyNwUehDSI=", "WCh6SkWubetB1YcI/F/rVkvutkVzkGh2GGoWpOv09Yo="], "CachedAssets": {"8WQUjZ1BO2rUfub9FNKR9AHr6cZyCoy/YQhBHTuJHug=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\css\\site.css", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5aab1of5qx", "Integrity": "zTlIXBXgwHpwqTG4OpHRTD/63O+p7UBYG9edjLWZlsI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 5605, "LastWriteTime": "2025-07-20T15:58:22.5192521+00:00"}, "3iNZutqUgC/5Ztiuhzn79RWurOVEJOowQl4/x9p56Gk=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\favicon.ico", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-20T15:24:06.4152981+00:00"}, "7VgZl7pDWg/LBTp3sxaYrbVQaOMAwFMdLgqcK8m8mnc=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\js\\site.js", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-20T15:24:06.4692248+00:00"}, "0jwOkv4c42bsSBsdFIJG79uHHaHLY05axhTWUR9UcUY=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-20T15:24:06.3077598+00:00"}, "G4aLXXC7/ad40VwtcCe1CgAnO8gfbj/SJf0BUmo1ZBA=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-20T15:24:06.3087646+00:00"}, "mg2NWLcKjUzDQrVR1vYlKkP6xpTse2TdPJPlpSZpzt0=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-20T15:24:06.3087646+00:00"}, "KAe0X0KUuu5ZOS/CAbTRYBqBHXtDp26sSGGHW6G/Hgo=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-20T15:24:06.3097662+00:00"}, "4+JatPv/Lmlqw4V5IrAITqkR1mln2GB1kg1GJK2TvLs=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-20T15:24:06.3107668+00:00"}, "BHiqUQAV6qLsQjIgIKo1hSvN8dtGTlAmjkybDtWMH54=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-20T15:24:06.3161067+00:00"}, "euttKNsKWdyn4Vsi76SNP0tGEyoryZa1eG2uXYO1ed8=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-20T15:24:06.3171069+00:00"}, "Jd44u405isrwsJNP98BgiupuwzzGgvD2611dLE+ESZ4=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-20T15:24:06.3191069+00:00"}, "9dLTZBfyRLUcJ3BR2XhibGQ6ZiOwO0cuwfAADk1S9pc=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-20T15:24:06.3191069+00:00"}, "i4/vOfST9KKxmY9kOcMkxjZDEmHZN65AKJxSlV/NzZg=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-20T15:24:06.320107+00:00"}, "t7qs7CtyibgsnH+tJlAeG6KeL9m9/om4nIVMFkMt6n0=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-20T15:24:06.320107+00:00"}, "Sw6Wsj8coSHx8tZybDktDeVG1vtzRASiOS/yA5XtYRs=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-20T15:24:06.3211069+00:00"}, "cPaMyfSgWkIq0V7Zcw03SDLH2fqoK0pNZLVEE58uRzk=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-20T15:24:06.3211069+00:00"}, "ihi5boac1hCXmA6wxAgcsdB9SPWN5BeVhsujvdmVTPo=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-20T15:24:06.3221083+00:00"}, "dEv7yqJeTBQAZIy4vsNg7gJFd+HnjGVisDjVmhQ1H44=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-20T15:24:06.3221083+00:00"}, "yCM3pds01Bc3yc9puSr/h4TvuOL6VAak1EL0gRORcb0=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-20T15:24:06.3231096+00:00"}, "nZAHo1pghM41QZMkJWaaBtpVS3/CNeS8QiRCBbwW/2U=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-20T15:24:06.3231096+00:00"}, "Evzat+vVwf3c7b1Hx3BeVSMgvh3dl8nzkMb5CK5BrXE=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-20T15:24:06.3241071+00:00"}, "dpqpgr3jf8Odldtd4QoUufXPZ1KslPN8mokkQTsViwA=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-20T15:24:06.3251062+00:00"}, "OOZJ0Hqmeda70PF6YG/J+atKFYJpvz2GdRV9NYuUO9k=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-20T15:24:06.3291598+00:00"}, "zIoyNujraGRthZxIjGjfTBAaTOsyTzbLdq0RqZDvcwY=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-20T15:24:06.3301679+00:00"}, "vnThyoVIRgshoUA4FkUTsMndGOCk7vQAMH8/13uOOEI=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-20T15:24:06.3321676+00:00"}, "kBgI4m63Qd/BpZ0uORiFptugbUotFiiFZKCJHs0t2Hg=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-20T15:24:06.3321676+00:00"}, "5ab4iAVehp/uFYcesrzgPg8e4G4xdgCh41YSE8kgkDs=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-20T15:24:06.3331694+00:00"}, "uhK+W2yOfvFUAoho2VkUBZ1sE9m3tpxMLoJnc4YZsFM=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-20T15:24:06.3351684+00:00"}, "N5OZiLx1uzOJDBLHLfPxg1QttzpvZVSi/jnILISAU6o=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-20T15:24:06.3381689+00:00"}, "rYBpPIFb6oaJIE31ATlPApfWj8ys/kCIVwEWTuE578Q=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-20T15:24:06.343729+00:00"}, "gGCtzogOEKD+r6DK63AxUkAcxjqIID6Gh4NDKhYq32Y=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-20T15:24:06.350728+00:00"}, "j4YVZNlXyf1FJOiC9XUhBVjQM1+9FWJLW7Ey6M8mv0c=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-20T15:24:06.3517275+00:00"}, "SwxE/YbFoWLCnkp55dsriOnJuulGgLk9ahbzmaaBNWE=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-20T15:24:06.3548714+00:00"}, "noPBsG4wd8YquzCpp02lsu4tyt91ggIwMusNHIicshs=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-20T15:24:06.3559047+00:00"}, "4wfC5Ky/Hz9QOhfTfJpDIRadcl8Xq6syjS587kkI+kM=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-20T15:24:06.3579039+00:00"}, "puCuW5xgHyHhxDh/NOvArOGbKpkIsT/dbObcLSJUsX8=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-20T15:24:06.3589055+00:00"}, "hQcYX9HFngViS3XOcDopewpem3gyGhJ41tUW+V0PdTY=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-20T15:24:06.3609027+00:00"}, "wLYufn8EYjKL1Y9hGOgLstip3AHwVo7gbjbE9SK+U1A=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-20T15:24:06.3619035+00:00"}, "9WI7r/eZIMQgHecahIr0tKI1gHFZtkMSo87gWZ8BfsY=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-20T15:24:06.3649014+00:00"}, "eHmiz6hkaLmH1hbiWjKe5LZEswoheaO1Fgy7hobVLpM=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-20T15:24:06.3649014+00:00"}, "h5hSU4BqxG2F9cBj5tBoZOeHQ6jGvIIKQZueSPjhtb8=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-20T15:24:06.366901+00:00"}, "1US+xPJmzpeL2JBVFruD4ktL1s+1uVhwWdNWqMJW1pU=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-20T15:24:06.368186+00:00"}, "hFmsMKORRt46dF1GCmIHNv0K5ANlzibyTs03gmhAv9o=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-20T15:24:06.3704583+00:00"}, "t21o1ELDXvtzuhXhEpNFqTZZHiMdriolNwDO5i4XfwA=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-20T15:24:06.3724671+00:00"}, "PL549ddgzpqqZXLTYeS2uASwjqFAnyyjzwpsjN1dU/w=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-20T15:24:06.3754656+00:00"}, "fNZhiVpwczA7C9qtt+s6zy/oti9ZSx28D1SkqIxTFU0=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-20T15:24:06.3784662+00:00"}, "6EEONtOVF8ldiQYRs4MQ3kzTiULM9bfB5M2S0GUZZNI=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-20T15:24:06.3794666+00:00"}, "PIP10+IodJtXnU+zFEOVx1Gzc8PWxolsvpTvAD7/LUw=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-20T15:24:06.3097662+00:00"}, "Alc8LNxbR8lBoDrOkT1g/MHR35uVQ+uvHyIqYQKCTe8=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-20T15:24:06.4182974+00:00"}, "AgkVT0oSDPTzHLgmwdxcznQV6DZThitVMMgNz/P66a4=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-20T15:24:06.4192984+00:00"}, "mS9ek/tVHLV/jH3l7Ru0sePw32DC8Bzy/V7XE+ByoBw=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-20T15:24:06.3181062+00:00"}, "7cgRYtNPwY+Uw8hzAHlwViAnA/9acbhyfWP8eq4QUHU=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-20T15:24:06.4122977+00:00"}, "HB/FNWIpyIqWFzgNQoatHcwu1ADeJmmy2gO83lncFzM=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-20T15:24:06.4142981+00:00"}, "fu4fssapi9iepp7mDoyOunL7/JMFev3Dc8Hk5X5UJpU=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-20T15:24:06.416298+00:00"}, "D+uMMLz6lQbzIRPfP5tp89BNyy5k6nt8hWmiGTFMkPk=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-20T15:24:06.416298+00:00"}, "YfJAoHTHDEwLlEtjbxJaqZixq75A6w2uL1Acvgczx2Q=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-20T15:24:06.3171069+00:00"}, "Ik273Strot8pUB6yOPZOKela9NRxsCxvSVyzwJbWScE=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-20T15:24:06.3835103+00:00"}, "3+WWaIG9yj7ZhiCi+G3PX5qhRXePYD12YUCpNKjIacs=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-20T15:24:06.3855107+00:00"}, "/N3hjzyUspmc9wxMQTsXpzZi3tz5dIO9VyDIa3O1Ed8=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-20T15:24:06.3935105+00:00"}, "sLkCqpOtDtgKTg0wvMsGqYLCVUcWO7lGGs5eV01T7N8=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-20T15:24:06.3958692+00:00"}, "1IfwesSS2Y2+twR2ru5y2peCpWwRdqiIRjatsFpki4A=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-20T15:24:06.397869+00:00"}, "pKRHWf9S/RJ8P56VUE+QAg/ID9DHpQYyVkym6ZNoCXg=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-20T15:24:06.4092985+00:00"}, "DkicDRvKC2A4L6MryHmOfl6cLRVCnyz/utvSFQ4Y2n4=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-20T15:24:06.3151047+00:00"}, "Lx2jyWox6EXVyZMSWH6P26jdPwSJ3teT/TytNSTytGU=": {"Identity": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\images\\pets\\default.jpg", "SourceId": "PetShopWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetShopWebsite\\wwwroot\\", "BasePath": "_content/PetShopWebsite", "RelativePath": "images/pets/default#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "50bu4ufwjk", "Integrity": "y8AnSptsWJPTLR1+yxQVzcIyWFHGArLXX18awlNjHwM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\pets\\default.jpg", "FileLength": 155, "LastWriteTime": "2025-07-20T15:37:43.2216591+00:00"}}, "CachedCopyCandidates": {}}