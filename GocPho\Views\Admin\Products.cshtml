@model List<GocPho.Models.Product>
@{
    ViewData["Title"] = "Quản lý sản phẩm";
    Layout = "_AdminLayout";
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="display-6 fw-bold">
                    <i class="fas fa-coffee text-warning me-2"></i>Quản lý sản phẩm
                </h1>
                <a href="@Url.Action("CreateProduct", "Admin")" class="btn btn-warning">
                    <i class="fas fa-plus me-2"></i>Thêm sản phẩm mới
                </a>
            </div>

            @if (Model.Any())
            {
                <div class="card shadow-sm border-0">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="bg-light">
                                    <tr>
                                        <th><PERSON><PERSON><PERSON><PERSON></th>
                                        <th>Tên sản phẩm</th>
                                        <th><PERSON>h mục</th>
                                        <th class="text-end">Giá</th>
                                        <th class="text-center">Trạng thái</th>
                                        <th class="text-center">Ngày tạo</th>
                                        <th class="text-center">Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var product in Model)
                                    {
                                        <tr>
                                            <td>
                                                <img src="@product.ImageUrl" alt="@product.Name" 
                                                     class="rounded" style="width: 50px; height: 50px; object-fit: cover;"
                                                     onerror="this.src='/images/default-coffee.svg'">
                                            </td>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1">@product.Name</h6>
                                                    <small class="text-muted">@product.Description</small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">@product.Category?.Name</span>
                                            </td>
                                            <td class="text-end fw-bold">@product.Price.ToString("N0") VNĐ</td>
                                            <td class="text-center">
                                                @if (product.IsAvailable)
                                                {
                                                    <span class="badge bg-success">Có sẵn</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">Hết hàng</span>
                                                }
                                            </td>
                                            <td class="text-center">
                                                <small class="text-muted">@product.CreatedAt.ToString("dd/MM/yyyy")</small>
                                            </td>
                                            <td class="text-center">
                                                <div class="btn-group btn-group-sm">
                                                    <a href="@Url.Action("EditProduct", "Admin", new { id = product.Id })" 
                                                       class="btn btn-outline-warning" title="Chỉnh sửa">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-outline-danger" 
                                                            onclick="deleteProduct(@product.Id, '@product.Name')" title="Xóa">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-coffee fa-4x text-muted mb-3"></i>
                    <h3 class="text-muted">Chưa có sản phẩm nào</h3>
                    <p class="text-muted mb-4">Hãy thêm sản phẩm đầu tiên cho quán cà phê của bạn.</p>
                    <a href="@Url.Action("CreateProduct", "Admin")" class="btn btn-warning btn-lg">
                        <i class="fas fa-plus me-2"></i>Thêm sản phẩm mới
                    </a>
                </div>
            }
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>Xác nhận xóa
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Bạn có chắc chắn muốn xóa sản phẩm <strong id="productName"></strong>?</p>
                <p class="text-muted">Hành động này không thể hoàn tác.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <form id="deleteForm" method="post" class="d-inline">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>Xóa
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function deleteProduct(productId, productName) {
            document.getElementById('productName').textContent = productName;
            document.getElementById('deleteForm').action = '@Url.Action("DeleteProduct", "Admin")/' + productId;
            
            var modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        }
    </script>
}
