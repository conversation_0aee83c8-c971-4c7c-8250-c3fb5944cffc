@{
    ViewData["Title"] = "Test Page";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4><i class="fas fa-check-circle"></i> Test Page</h4>
                </div>
                <div class="card-body">
                    <h5>Database Connection Test</h5>
                    <p class="alert alert-info">@ViewBag.Message</p>
                    
                    <h5>Current Time</h5>
                    <p>@DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")</p>
                    
                    <h5>Navigation Links</h5>
                    <div class="d-flex gap-2">
                        <a asp-action="Index" class="btn btn-primary">Home</a>
                        <a asp-controller="Pets" asp-action="Index" class="btn btn-secondary">Pets</a>
                        <a asp-controller="Account" asp-action="Login" class="btn btn-outline-primary">Login</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
