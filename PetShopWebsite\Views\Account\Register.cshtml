@model PetShopWebsite.ViewModels.RegisterViewModel
@{
    ViewData["Title"] = "Đăng ký";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-header bg-success text-white text-center">
                    <h4><i class="fas fa-user-plus"></i> Đăng ký tài khoản</h4>
                </div>
                <div class="card-body">
                    <form asp-action="Register" asp-route-returnurl="@ViewData["ReturnUrl"]" method="post">
                        <div asp-validation-summary="All" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="FullName" class="form-label"></label>
                                <input asp-for="FullName" class="form-control" placeholder="Nhập họ tên đầy đủ" />
                                <span asp-validation-for="FullName" class="text-danger"></span>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label"></label>
                                <input asp-for="Email" class="form-control" placeholder="Nhập email" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="PhoneNumber" class="form-label"></label>
                                <input asp-for="PhoneNumber" class="form-control" placeholder="Nhập số điện thoại" />
                                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label asp-for="DateOfBirth" class="form-label"></label>
                                <input asp-for="DateOfBirth" class="form-control" type="date" />
                                <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label asp-for="Address" class="form-label"></label>
                            <textarea asp-for="Address" class="form-control" rows="2" placeholder="Nhập địa chỉ (không bắt buộc)"></textarea>
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Password" class="form-label"></label>
                                <input asp-for="Password" class="form-control" placeholder="Nhập mật khẩu" />
                                <span asp-validation-for="Password" class="text-danger"></span>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label asp-for="ConfirmPassword" class="form-label"></label>
                                <input asp-for="ConfirmPassword" class="form-control" placeholder="Xác nhận mật khẩu" />
                                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-user-plus"></i> Đăng ký
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <p class="mb-0">
                        Đã có tài khoản? 
                        <a asp-action="Login" asp-route-returnurl="@ViewData["ReturnUrl"]" class="text-decoration-none">
                            Đăng nhập ngay
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
