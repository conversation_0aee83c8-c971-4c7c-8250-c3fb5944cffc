@model PetShopWebsite.Models.Category
@{
    ViewData["Title"] = Model.Name + " - <PERSON>";
    var pets = ViewBag.Pets as List<PetShopWebsite.Models.Pet>;
    var petCount = (int)ViewBag.PetCount;
}

<div class="container my-5">
    <!-- Category Header -->
    <div class="category-header text-center mb-5 p-5 rounded-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="text-white">
            <div class="category-icon mb-4">
                <img src="@(Model.ImageUrl ?? "/images/categories/default.jpg")" 
                     alt="@Model.Name" 
                     class="img-fluid rounded-circle border border-white border-4" 
                     style="width: 150px; height: 150px; object-fit: cover;">
            </div>
            <h1 class="display-4 fw-bold mb-3">@Model.Name</h1>
            @if (!string.IsNullOrEmpty(Model.Description))
            {
                <p class="lead mb-4">@Model.Description</p>
            }
            <div class="category-stats">
                <span class="badge bg-warning text-dark px-4 py-2 rounded-pill fs-6 me-3">
                    <i class="fas fa-paw me-2"></i>@petCount thú cưng
                </span>
                <span class="badge bg-light text-dark px-4 py-2 rounded-pill fs-6">
                    <i class="fas fa-calendar me-2"></i>Cập nhật @Model.CreatedAt.ToString("dd/MM/yyyy")
                </span>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a asp-controller="Home" asp-action="Index" class="text-decoration-none">
                    <i class="fas fa-home me-1"></i>Trang chủ
                </a>
            </li>
            <li class="breadcrumb-item">
                <a asp-controller="Categories" asp-action="Index" class="text-decoration-none">
                    <i class="fas fa-list me-1"></i>Danh mục
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">@Model.Name</li>
        </ol>
    </nav>

    <!-- Category Info & Actions -->
    <div class="row mb-5">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <h4 class="fw-bold text-primary mb-3">
                        <i class="fas fa-info-circle me-2"></i>Thông Tin Danh Mục
                    </h4>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <label class="fw-semibold text-muted">Tên danh mục:</label>
                                <p class="mb-0">@Model.Name</p>
                            </div>
                            <div class="info-item mb-3">
                                <label class="fw-semibold text-muted">Số lượng thú cưng:</label>
                                <p class="mb-0">@petCount con</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <label class="fw-semibold text-muted">Thứ tự hiển thị:</label>
                                <p class="mb-0">#@Model.DisplayOrder</p>
                            </div>
                            <div class="info-item mb-3">
                                <label class="fw-semibold text-muted">Trạng thái:</label>
                                <span class="badge @(Model.IsActive ? "bg-success" : "bg-secondary")">
                                    @(Model.IsActive ? "Đang hoạt động" : "Tạm dừng")
                                </span>
                            </div>
                        </div>
                    </div>
                    @if (!string.IsNullOrEmpty(Model.Description))
                    {
                        <div class="info-item mt-3">
                            <label class="fw-semibold text-muted">Mô tả:</label>
                            <p class="mb-0">@Model.Description</p>
                        </div>
                    }
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4 text-center">
                    <h5 class="fw-bold text-primary mb-3">
                        <i class="fas fa-rocket me-2"></i>Hành Động Nhanh
                    </h5>
                    <div class="d-grid gap-2">
                        <a asp-controller="Pets" asp-action="Category" asp-route-id="@Model.Id" 
                           class="btn btn-primary btn-lg rounded-pill">
                            <i class="fas fa-eye me-2"></i>Xem tất cả thú cưng
                        </a>
                        <a asp-controller="Pets" asp-action="Index" asp-route-categoryId="@Model.Id" 
                           class="btn btn-outline-primary rounded-pill">
                            <i class="fas fa-filter me-2"></i>Lọc theo danh mục
                        </a>
                        <a asp-controller="Categories" asp-action="Index" 
                           class="btn btn-outline-secondary rounded-pill">
                            <i class="fas fa-arrow-left me-2"></i>Về danh sách danh mục
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Featured Pets in Category -->
    @if (pets != null && pets.Any())
    {
        <div class="featured-pets mb-5">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="fw-bold text-primary mb-0">
                    <i class="fas fa-star text-warning me-2"></i>Thú Cưng Nổi Bật
                </h4>
                <a asp-controller="Pets" asp-action="Category" asp-route-id="@Model.Id" 
                   class="btn btn-outline-primary rounded-pill">
                    Xem tất cả <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
            
            <div class="row g-4">
                @foreach (var pet in pets.Take(6))
                {
                    <div class="col-lg-4 col-md-6">
                        <div class="pet-card card border-0 shadow-sm h-100">
                            <div class="position-relative">
                                <img src="@(pet.MainImageUrl ?? "/images/pets/default.jpg")" 
                                     alt="@pet.Name" 
                                     class="card-img-top" 
                                     style="height: 250px; object-fit: cover;">
                                @if (pet.IsFeatured)
                                {
                                    <span class="badge bg-warning text-dark position-absolute top-0 start-0 m-2">
                                        <i class="fas fa-star me-1"></i>Nổi bật
                                    </span>
                                }
                                @if (pet.SalePrice.HasValue && pet.SalePrice < pet.Price)
                                {
                                    <span class="badge bg-danger position-absolute top-0 end-0 m-2">
                                        Giảm giá
                                    </span>
                                }
                            </div>
                            <div class="card-body p-3">
                                <h6 class="fw-bold text-primary mb-2">@pet.Name</h6>
                                <p class="text-muted small mb-2">
                                    <i class="fas fa-tag me-1"></i>@pet.Breed
                                    <span class="mx-2">•</span>
                                    <i class="fas fa-birthday-cake me-1"></i>@pet.Age tháng
                                </p>
                                <div class="price-section mb-2">
                                    @if (pet.SalePrice.HasValue && pet.SalePrice < pet.Price)
                                    {
                                        <span class="text-danger fw-bold">@pet.SalePrice.Value.ToString("N0") ₫</span>
                                        <small class="text-muted text-decoration-line-through ms-1">@pet.Price.ToString("N0") ₫</small>
                                    }
                                    else
                                    {
                                        <span class="text-primary fw-bold">@pet.Price.ToString("N0") ₫</span>
                                    }
                                </div>
                                <div class="d-grid">
                                    <a asp-controller="Pets" asp-action="Details" asp-route-id="@pet.Id" 
                                       class="btn btn-outline-primary btn-sm rounded-pill">
                                        <i class="fas fa-eye me-1"></i>Xem chi tiết
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    }
    else
    {
        <!-- No Pets in Category -->
        <div class="no-pets text-center py-5">
            <div class="empty-state">
                <i class="fas fa-paw fa-5x text-muted mb-4"></i>
                <h4 class="text-muted mb-3">Chưa có thú cưng nào trong danh mục này</h4>
                <p class="text-muted mb-4">Chúng tôi đang cập nhật thêm những chú thú cưng đáng yêu. Vui lòng quay lại sau!</p>
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a asp-controller="Categories" asp-action="Index" class="btn btn-primary btn-lg rounded-pill">
                        <i class="fas fa-list me-2"></i>Xem danh mục khác
                    </a>
                    <a asp-controller="Pets" asp-action="Index" class="btn btn-outline-primary btn-lg rounded-pill">
                        <i class="fas fa-paw me-2"></i>Xem tất cả thú cưng
                    </a>
                </div>
            </div>
        </div>
    }
</div>

<style>
.pet-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 15px !important;
}

.pet-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.1) !important;
}

.pet-card img {
    border-radius: 15px 15px 0 0 !important;
}

.info-item label {
    font-size: 0.9rem;
}

.empty-state i {
    animation: bounce 2s ease-in-out infinite;
}

@@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}
</style>
