@model PetShopWebsite.ViewModels.ChangePasswordViewModel
@{
    ViewData["Title"] = "Đổi mật khẩu";
}

<div class="container mt-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user"></i> Tài khoản của tôi</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a asp-action="Profile" class="list-group-item list-group-item-action">
                        <i class="fas fa-user-edit"></i> Thông tin cá nhân
                    </a>
                    <a href="#" class="list-group-item list-group-item-action active">
                        <i class="fas fa-key"></i> Đ<PERSON>i mật khẩu
                    </a>
                    <a asp-controller="Orders" asp-action="Index" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-bag"></i> Đơn hàng của tôi
                    </a>
                    <a asp-controller="Cart" asp-action="Index" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-cart"></i> Giỏ hàng
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-key"></i> Đổi mật khẩu</h5>
                </div>
                <div class="card-body">
                    <form asp-action="ChangePassword" method="post">
                        <div asp-validation-summary="All" class="text-danger mb-3"></div>
                        
                        <div class="mb-3">
                            <label asp-for="OldPassword" class="form-label"></label>
                            <input asp-for="OldPassword" class="form-control" />
                            <span asp-validation-for="OldPassword" class="text-danger"></span>
                        </div>
                        
                        <div class="mb-3">
                            <label asp-for="NewPassword" class="form-label"></label>
                            <input asp-for="NewPassword" class="form-control" />
                            <span asp-validation-for="NewPassword" class="text-danger"></span>
                            <div class="form-text">Mật khẩu phải có ít nhất 6 ký tự và chứa ít nhất 1 chữ số.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label asp-for="ConfirmPassword" class="form-label"></label>
                            <input asp-for="ConfirmPassword" class="form-control" />
                            <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Đổi mật khẩu
                            </button>
                            <a asp-action="Profile" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
