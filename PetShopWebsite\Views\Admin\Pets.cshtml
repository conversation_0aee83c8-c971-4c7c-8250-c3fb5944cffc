@model IEnumerable<PetShopWebsite.Models.Pet>
@{
    ViewData["Title"] = "Quản Lý T<PERSON>ú <PERSON>";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">Quản Lý Thú Cưng</h1>
    <a asp-controller="Pets" asp-action="Create" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Thêm Thú <PERSON>ng Mới
    </a>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">T<PERSON><PERSON> kiếm</label>
                <input type="text" name="search" value="@ViewBag.Search" class="form-control" placeholder="Tê<PERSON>, giống, mô tả...">
            </div>
            <div class="col-md-3">
                <label class="form-label">Danh mục</label>
                <select name="categoryId" class="form-select">
                    <option value="0">Tất cả danh mục</option>
                    @foreach (var category in ViewBag.Categories as List<PetShopWebsite.Models.Category>)
                    {
                        <option value="@category.Id" selected="@(ViewBag.CategoryId == category.Id)">
                            @category.Name
                        </option>
                    }
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>Tìm kiếm
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a asp-action="Pets" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Xóa bộ lọc
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Pets Table -->
<div class="card">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold">Danh Sách Thú Cưng</h6>
    </div>
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Hình ảnh</th>
                            <th>Tên</th>
                            <th>Danh mục</th>
                            <th>Giá</th>
                            <th>Tồn kho</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var pet in Model)
                        {
                            <tr>
                                <td>
                                    <img src="@(pet.MainImageUrl ?? "/images/pets/default.jpg")" 
                                         alt="@pet.Name" 
                                         class="rounded" 
                                         style="width: 60px; height: 60px; object-fit: cover;">
                                </td>
                                <td>
                                    <div>
                                        <strong>@pet.Name</strong>
                                        <br>
                                        <small class="text-muted">@pet.Breed</small>
                                    </div>
                                </td>
                                <td>@pet.Category?.Name</td>
                                <td>
                                    @if (pet.SalePrice.HasValue && pet.SalePrice < pet.Price)
                                    {
                                        <span class="text-danger fw-bold">@pet.SalePrice.Value.ToString("N0") ₫</span>
                                        <br>
                                        <small class="text-muted text-decoration-line-through">@pet.Price.ToString("N0") ₫</small>
                                    }
                                    else
                                    {
                                        <span class="fw-bold">@pet.Price.ToString("N0") ₫</span>
                                    }
                                </td>
                                <td>
                                    <span class="badge @(pet.StockQuantity <= 2 ? "bg-danger" : pet.StockQuantity <= 5 ? "bg-warning" : "bg-success")">
                                        @pet.StockQuantity
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex flex-column gap-1">
                                        <span class="badge @(pet.IsActive ? "bg-success" : "bg-secondary")">
                                            @(pet.IsActive ? "Hoạt động" : "Ẩn")
                                        </span>
                                        @if (pet.IsFeatured)
                                        {
                                            <span class="badge bg-warning text-dark">Nổi bật</span>
                                        }
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-controller="Pets" asp-action="Details" asp-route-id="@pet.Id" 
                                           class="btn btn-sm btn-outline-info" title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-controller="Pets" asp-action="Edit" asp-route-id="@pet.Id" 
                                           class="btn btn-sm btn-outline-primary" title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-warning toggle-featured" 
                                                data-pet-id="@pet.Id" data-featured="@pet.IsFeatured.ToString().ToLower()"
                                                title="@(pet.IsFeatured ? "Bỏ nổi bật" : "Đặt nổi bật")">
                                            <i class="fas fa-star"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary toggle-status" 
                                                data-pet-id="@pet.Id" data-active="@pet.IsActive.ToString().ToLower()"
                                                title="@(pet.IsActive ? "Ẩn" : "Hiển thị")">
                                            <i class="fas @(pet.IsActive ? "fa-eye-slash" : "fa-eye")"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if (ViewBag.TotalPages > 1)
            {
                <nav aria-label="Pagination" class="mt-3">
                    <ul class="pagination justify-content-center">
                        @if (ViewBag.CurrentPage > 1)
                        {
                            <li class="page-item">
                                <a class="page-link" asp-action="Pets" 
                                   asp-route-page="@(ViewBag.CurrentPage - 1)"
                                   asp-route-search="@ViewBag.Search"
                                   asp-route-categoryId="@ViewBag.CategoryId">
                                    <i class="fas fa-chevron-left"></i> Trước
                                </a>
                            </li>
                        }
                        
                        @for (int i = Math.Max(1, ViewBag.CurrentPage - 2); i <= Math.Min(ViewBag.TotalPages, ViewBag.CurrentPage + 2); i++)
                        {
                            <li class="page-item @(i == ViewBag.CurrentPage ? "active" : "")">
                                <a class="page-link" asp-action="Pets" 
                                   asp-route-page="@i"
                                   asp-route-search="@ViewBag.Search"
                                   asp-route-categoryId="@ViewBag.CategoryId">@i</a>
                            </li>
                        }
                        
                        @if (ViewBag.CurrentPage < ViewBag.TotalPages)
                        {
                            <li class="page-item">
                                <a class="page-link" asp-action="Pets" 
                                   asp-route-page="@(ViewBag.CurrentPage + 1)"
                                   asp-route-search="@ViewBag.Search"
                                   asp-route-categoryId="@ViewBag.CategoryId">
                                    Sau <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        }
                    </ul>
                </nav>
            }
        }
        else
        {
            <div class="text-center py-5">
                <i class="fas fa-paw fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Không tìm thấy thú cưng nào</h5>
                <p class="text-muted">Hãy thử thay đổi bộ lọc hoặc thêm thú cưng mới.</p>
                <a asp-controller="Pets" asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Thêm Thú Cưng Mới
                </a>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Toggle featured status
            $('.toggle-featured').on('click', function() {
                const petId = $(this).data('pet-id');
                const button = $(this);
                
                $.post('/Admin/TogglePetFeatured', { petId: petId })
                    .done(function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.message);
                        }
                    })
                    .fail(function() {
                        alert('Có lỗi xảy ra. Vui lòng thử lại.');
                    });
            });

            // Toggle active status
            $('.toggle-status').on('click', function() {
                const petId = $(this).data('pet-id');
                const button = $(this);
                
                $.post('/Admin/TogglePetStatus', { petId: petId })
                    .done(function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.message);
                        }
                    })
                    .fail(function() {
                        alert('Có lỗi xảy ra. Vui lòng thử lại.');
                    });
            });
        });
    </script>
}
