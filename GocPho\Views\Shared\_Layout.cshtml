﻿<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Góc phố Coffee</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/GocPho.styles.css" asp-append-version="true" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
            <div class="container">
                <a class="navbar-brand fw-bold" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-coffee text-warning me-2"></i>Góc phố Coffee
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-home me-1"></i>Trang chủ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Product" asp-action="Menu">
                                <i class="fas fa-list me-1"></i>Menu
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Home" asp-action="About">
                                <i class="fas fa-info-circle me-1"></i>Giới thiệu
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Home" asp-action="Contact">
                                <i class="fas fa-phone me-1"></i>Liên hệ
                            </a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        @if (User.Identity?.IsAuthenticated == true)
                        {
                            <li class="nav-item">
                                <a class="nav-link position-relative" asp-controller="Cart" asp-action="Index">
                                    <i class="fas fa-shopping-cart me-1"></i>Giỏ hàng
                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="cart-count">
                                        0
                                    </span>
                                </a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user me-1"></i>@User.Identity.Name
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" asp-controller="Order" asp-action="MyOrders">
                                        <i class="fas fa-receipt me-1"></i>Đơn hàng của tôi
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="Account" asp-action="Profile">
                                        <i class="fas fa-user-edit me-1"></i>Thông tin cá nhân
                                    </a></li>
                                    @if (User.IsInRole("Admin"))
                                    {
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" asp-controller="Admin" asp-action="Index">
                                            <i class="fas fa-cog me-1"></i>Quản trị
                                        </a></li>
                                    }
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item">
                                                <i class="fas fa-sign-out-alt me-1"></i>Đăng xuất
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Account" asp-action="Login">
                                    <i class="fas fa-sign-in-alt me-1"></i>Đăng nhập
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Account" asp-action="Register">
                                    <i class="fas fa-user-plus me-1"></i>Đăng ký
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main role="main" style="margin-top: 76px;">
        @if (TempData["Success"] != null)
        {
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>@TempData["Success"]
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        }
        @if (TempData["Error"] != null)
        {
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>@TempData["Error"]
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        }
        @RenderBody()
    </main>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @if (User.Identity?.IsAuthenticated == true)
    {
        <script>
            // Load cart count on page load
            $(document).ready(function() {
                loadCartCount();
            });

            function loadCartCount() {
                $.get('/Cart/GetCartCount', function(data) {
                    $('#cart-count').text(data.count);
                    if (data.count > 0) {
                        $('#cart-count').show();
                    } else {
                        $('#cart-count').hide();
                    }
                });
            }

            // Global function to update cart count
            window.updateCartCount = function(count) {
                $('#cart-count').text(count);
                if (count > 0) {
                    $('#cart-count').show();
                } else {
                    $('#cart-count').hide();
                }
            };
        </script>
    }

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
