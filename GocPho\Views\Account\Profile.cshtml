@model GocPho.Models.RegisterViewModel

@{
    ViewData["Title"] = "Thông tin cá nhân";
}

<style>
    .profile-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }

    .page-header {
        text-align: center;
        margin-bottom: 40px;
        padding: 40px 0;
        background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
        color: white;
        border-radius: 15px;
    }

    .page-header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        font-weight: 700;
    }

    .page-header p {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0;
    }

    .profile-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 30px;
    }

    .profile-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 30px;
        text-align: center;
        border-bottom: 1px solid #dee2e6;
    }

    .profile-avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 3rem;
        color: white;
    }

    .profile-name {
        font-size: 1.5rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 5px;
    }

    .profile-email {
        color: #6c757d;
        font-size: 1rem;
    }

    .profile-body {
        padding: 30px;
    }

    .section-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #8B4513;
        margin-bottom: 25px;
        padding-bottom: 10px;
        border-bottom: 2px solid #8B4513;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
        margin-bottom: 30px;
    }

    .info-item {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        border-left: 4px solid #8B4513;
    }

    .info-label {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 8px;
        text-transform: uppercase;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .info-value {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        word-break: break-word;
    }

    .action-buttons {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 30px;
    }

    .btn-action {
        padding: 12px 25px;
        border-radius: 25px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
        color: white;
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        color: white;
    }

    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }

    .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        color: white;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        border: 1px solid #dee2e6;
    }

    .stat-icon {
        font-size: 2rem;
        margin-bottom: 10px;
        color: #8B4513;
    }

    .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 0.9rem;
        color: #6c757d;
        text-transform: uppercase;
        font-weight: 600;
    }

    @@media (max-width: 768px) {
        .profile-container {
            padding: 10px;
        }

        .page-header {
            padding: 30px 20px;
        }

        .page-header h1 {
            font-size: 2rem;
        }

        .profile-body {
            padding: 20px;
        }

        .info-grid {
            grid-template-columns: 1fr;
        }

        .action-buttons {
            flex-direction: column;
            align-items: center;
        }

        .btn-action {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>

<div class="profile-container">
    <div class="page-header">
        <h1>👤 Thông tin cá nhân</h1>
        <p>Quản lý thông tin tài khoản và cài đặt của bạn</p>
    </div>

    <div class="profile-card">
        <div class="profile-header">
            <div class="profile-avatar">
                👤
            </div>
            <div class="profile-name">@Model.FullName</div>
            <div class="profile-email">@Model.Email</div>
        </div>

        <div class="profile-body">
            <!-- Thống kê nhanh -->
            <h3 class="section-title">
                📊 Thống kê
            </h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📦</div>
                    <div class="stat-number">0</div>
                    <div class="stat-label">Đơn hàng</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">☕</div>
                    <div class="stat-number">0</div>
                    <div class="stat-label">Sản phẩm đã mua</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">💰</div>
                    <div class="stat-number">0 VNĐ</div>
                    <div class="stat-label">Tổng chi tiêu</div>
                </div>
            </div>

            <!-- Thông tin chi tiết -->
            <h3 class="section-title">
                ℹ️ Thông tin chi tiết
            </h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">
                        📧 Email
                    </div>
                    <div class="info-value">@Model.Email</div>
                </div>

                <div class="info-item">
                    <div class="info-label">
                        👤 Họ và tên
                    </div>
                    <div class="info-value">@Model.FullName</div>
                </div>

                <div class="info-item">
                    <div class="info-label">
                        📱 Số điện thoại
                    </div>
                    <div class="info-value">
                        @if (!string.IsNullOrEmpty(Model.PhoneNumber))
                        {
                            @Model.PhoneNumber
                        }
                        else
                        {
                            <span style="color: #6c757d; font-style: italic;">Chưa cập nhật</span>
                        }
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-label">
                        📅 Ngày tham gia
                    </div>
                    <div class="info-value">@DateTime.Now.ToString("dd/MM/yyyy")</div>
                </div>
            </div>

            <!-- Nút hành động -->
            <div class="action-buttons">
                <a href="@Url.Action("EditProfile")" class="btn-action btn-primary">
                    ✏️ Chỉnh sửa thông tin
                </a>
                <a href="#" class="btn-action btn-secondary">
                    🔒 Đổi mật khẩu
                </a>
                <a href="@Url.Action("MyOrders", "Order")" class="btn-action btn-success">
                    📋 Xem đơn hàng
                </a>
            </div>
        </div>
    </div>
</div>
